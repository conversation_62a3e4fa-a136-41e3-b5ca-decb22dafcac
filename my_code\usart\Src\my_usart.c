#include "my_usart.h"
#include "bsp_system.h" // 访问系统参数
#include "ad9959.h"     // AD9959控制函数
#include "my_hmi.h"     // HMI通信

// 串口1的缓冲区和状态变量
uint8_t rxBuffer1[RX_BUFFER_SIZE];     ///< 串口1接收缓冲区
uint8_t rxTemp1;                       ///< 串口1中断接收临时变量
uint16_t rxIndex1 = 0;                 ///< 串口1当前接收缓冲区索引
volatile uint8_t commandReceived1 = 0; ///< 串口1接收到命令标志

// 串口3的缓冲区和状态变量
uint8_t rxBuffer3[RX_BUFFER_SIZE];     ///< 串口3接收缓冲区
uint8_t rxTemp3;                       ///< 串口3中断接收临时变量
uint16_t rxIndex3 = 0;                 ///< 串口3当前接收缓冲区索引
volatile uint8_t commandReceived3 = 0; ///< 串口3接收到命令标志

// 串口2数据包缓冲区和变量
uint8_t rxBuffer2[RX_BUFFER_SIZE]; ///< 串口2接收缓冲区
uint8_t rxTemp2;                   ///< 串口2中断接收临时变量
uint16_t rxIndex2 = 0;             ///< 串口2当前接收缓冲区索引
volatile uint8_t frameStarted = 0; ///< 帧开始标志

u8 Adjust = 50;

// ========== FPGA控制参数 ==========
// 调制度参数 (百分比，0-100%)
uint8_t modulation_depth_ch0 = 50; // CH0调制度，默认50%
uint8_t modulation_depth_ch1 = 50; // CH1调制度，默认50%

// FPGA命令定义 - 完整映射
// 相位命令 (0-3)
#define FPGA_CMD_CH0_PHASE_PLUS "0"
#define FPGA_CMD_CH0_PHASE_MINUS "1"
#define FPGA_CMD_CH1_PHASE_PLUS "2"
#define FPGA_CMD_CH1_PHASE_MINUS "3"

// 时延命令 (4-7)
#define FPGA_CMD_CH0_TIME_PLUS "4"
#define FPGA_CMD_CH0_TIME_MINUS "5"
#define FPGA_CMD_CH1_TIME_PLUS "6"
#define FPGA_CMD_CH1_TIME_MINUS "7"

// 调制度命令 (9/A/B/C)
#define FPGA_CMD_CH0_DEPTH_PLUS "9"
#define FPGA_CMD_CH0_DEPTH_MINUS "A"
#define FPGA_CMD_CH1_DEPTH_PLUS "B"
#define FPGA_CMD_CH1_DEPTH_MINUS "C"

// 步进值定义
#define DEPTH_STEP_PERCENT 10 // 调制度步进10%

// 限制值定义
#define DEPTH_MIN_PERCENT 0   // 调制度最小值0%
#define DEPTH_MAX_PERCENT 100 // 调制度最大值100%

// ========== 时延控制参数宏定义 ==========
// 时延步进值定义
#define TIME_DELAY_STEP_NS 30 // 时延步进30ns
// 时延限制值定义
#define TIME_DELAY_MIN_NS 50  // 时延最小值50ns
#define TIME_DELAY_MAX_NS 200 // 时延最大值200ns

/**
 * @brief 通过串口3发送命令给FPGA
 * @param cmd_str 要发送的命令字符串
 */
void Send_FPGA_Command(const char *cmd_str)
{
    HAL_UART_Transmit(&huart3, (uint8_t *)cmd_str, strlen(cmd_str), 100);
    HAL_UART_Transmit(&huart3, (uint8_t *)"\r\n", 2, 100); // 添加换行符
}

/**
 * @brief 计算并显示相对时延信息
 */
void Display_Relative_Delay_Info(void)
{
    int16_t relative_delay = (int16_t)time_delay_ns_ch1 - (int16_t)time_delay_ns_ch0;
    my_printf(&huart1, "Relative delay (CH1-CH0): %d ns\r\n", relative_delay);
}

/**
 * @brief 发送峰峰值信息给FPGA (调制度变化时使用)
 * @param channel 通道 (0或1)
 */
void Send_Peak_Value_To_FPGA(uint8_t channel)
{
    uint16_t peak_value;

    if (channel == 0)
    {
        peak_value = amp_value_mv_ch0;
    }
    else
    {
        peak_value = amp_value_mv_ch1;
    }

    // 计算四分之一峰峰值（向上取整）
    uint16_t quarter_peak = (peak_value + 3) / 4; // 向上取整

    // 只发送纯数字给FPGA（因为FPGA已经从之前的命令知道是哪个通道了）
    my_printf(&huart3, "%d\r\n", quarter_peak);

    // 调试输出
    my_printf(&huart1, "CH%d Peak Value: %dmV, Quarter: %dmV\r\n",
              channel, peak_value, quarter_peak);
}

/**
 * @brief 格式化打印并通过指定串口发送
 * @param huart 串口句柄
 * @param format 格式化字符串
 * @param ... 可变参数
 * @return 返回发送的字节数
 */
int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
    char buffer[512]; // 临时缓冲区，用于存储格式化后的字符串
    va_list arg;      // 可变参数列表
    int len;

    va_start(arg, format);                                // 初始化可变参数列表
    len = vsnprintf(buffer, sizeof(buffer), format, arg); // 格式化字符串
    va_end(arg);                                          // 清理可变参数列表

    HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF); // 发送格式化后的字符串
    return len;
}

/**
 * @brief 解析串口1命令并更新HMI显示
 * @param command 接收到的命令字符串
 */
void Parse_Command_UART1(char *command)
{
    // ========== CH0 频率控制命令 ==========
    if (strcmp(command, "ch0_freq_plus") == 0)
    {
        if (freq_value_ch0 + FREQ_STEP_HZ <= FREQ_MAX_HZ)
        {
            freq_value_ch0 += FREQ_STEP_HZ;
            Update_Channel_Output(0);
            Set_Parameter_Changed(); // 设置参数已更改标志

            // 更新HMI显示
            HMI_Send_Float("page0.n0", freq_value_ch0 / 1000000.0f, 1); // CH0频率显示，保留1位小数
            HMI_Send_String("page0.information", "CH0 Freq increased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH0 Max freq limit: 40MHz");
        }
    }
    else if (strcmp(command, "ch0_freq_minus") == 0)
    {
        if (freq_value_ch0 - FREQ_STEP_HZ >= FREQ_MIN_HZ)
        {
            freq_value_ch0 -= FREQ_STEP_HZ;
            Update_Channel_Output(0);
            Set_Parameter_Changed();

            // 更新HMI显示
            HMI_Send_Float("page0.n0", freq_value_ch0 / 1000000.0f, 1); // CH0频率显示
            HMI_Send_String("page0.information", "CH0 Freq decreased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH0 Min freq limit: 30MHz");
        }
    }

    // ========== CH1 频率控制命令 ==========
    else if (strcmp(command, "ch1_freq_plus") == 0)
    {
        if (freq_value_ch1 + FREQ_STEP_HZ <= FREQ_MAX_HZ)
        {
            freq_value_ch1 += FREQ_STEP_HZ;
            Update_Channel_Output(1);
            Set_Parameter_Changed();

            // 更新HMI显示
            HMI_Send_Float("page0.n1", freq_value_ch1 / 1000000.0f, 1); // CH1频率显示
            HMI_Send_String("page0.information", "CH1 Freq increased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH1 Max freq limit: 40MHz");
        }
    }
    else if (strcmp(command, "ch1_freq_minus") == 0)
    {
        if (freq_value_ch1 - FREQ_STEP_HZ >= FREQ_MIN_HZ)
        {
            freq_value_ch1 -= FREQ_STEP_HZ;
            Update_Channel_Output(1);
            Set_Parameter_Changed();

            // 更新HMI显示
            HMI_Send_Float("page0.n1", freq_value_ch1 / 1000000.0f, 1); // CH1频率显示
            HMI_Send_String("page0.information", "CH1 Freq decreased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH1 Min freq limit: 30MHz");
        }
    }

    // ========== CH0 幅度控制命令 ==========
    else if (strcmp(command, "ch0_amp_plus") == 0)
    {
        if (amp_value_mv_ch0 + AMP_STEP_MV <= AMP_MAX_MV)
        {
            amp_value_mv_ch0 += AMP_STEP_MV;
            Update_Channel_Output(0);
            Set_Parameter_Changed();

            // 更新HMI显示
            HMI_Send_Int("page0.n2", amp_value_mv_ch0); // CH0幅度显示
            HMI_Send_String("page0.information", "CH0 Amplitude increased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH0 Max amplitude: 250mV");
        }
    }
    else if (strcmp(command, "ch0_amp_minus") == 0)
    {
        if (amp_value_mv_ch0 - AMP_STEP_MV >= AMP_MIN_MV)
        {
            amp_value_mv_ch0 -= AMP_STEP_MV;
            Update_Channel_Output(0);
            Set_Parameter_Changed();

            // 更新HMI显示
            HMI_Send_Int("page0.n2", amp_value_mv_ch0); // CH0幅度显示
            HMI_Send_String("page0.information", "CH0 Amplitude decreased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH0 Min amplitude: 25mV");
        }
    }

    // ========== CH1 幅度控制命令 ==========
    else if (strcmp(command, "ch1_amp_plus") == 0)
    {
        if (amp_value_mv_ch1 + AMP_STEP_MV <= AMP_MAX_MV)
        {
            amp_value_mv_ch1 += AMP_STEP_MV;
            Update_Channel_Output(1);
            Set_Parameter_Changed();

            // 更新HMI显示
            HMI_Send_Int("page0.n3", amp_value_mv_ch1); // CH1幅度显示
            HMI_Send_String("page0.information", "CH1 Amplitude increased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH1 Max amplitude: 250mV");
        }
    }
    else if (strcmp(command, "ch1_amp_minus") == 0)
    {
        if (amp_value_mv_ch1 - AMP_STEP_MV >= AMP_MIN_MV)
        {
            amp_value_mv_ch1 -= AMP_STEP_MV;
            Update_Channel_Output(1);
            Set_Parameter_Changed();

            // 更新HMI显示
            HMI_Send_Int("page0.n3", amp_value_mv_ch1); // CH1幅度显示
            HMI_Send_String("page0.information", "CH1 Amplitude decreased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH1 Min amplitude: 25mV");
        }
    }

    // ========== CH0 相位控制命令 ==========
    else if (strcmp(command, "ch0_pha_plus") == 0)
    {
        if (phase_value_deg_ch0 + PHASE_STEP_DEG <= PHASE_MAX_DEG)
        {
            phase_value_deg_ch0 += PHASE_STEP_DEG;
            Update_Channel_Output(0);
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH0_PHASE_PLUS);

            // 更新HMI显示
            HMI_Send_Int("page0.n4", phase_value_deg_ch0); // CH0相位显示
            HMI_Send_String("page0.information", "CH0 Phase increased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH0 Max phase: 180°");
        }
    }
    else if (strcmp(command, "ch0_pha_minus") == 0)
    {
        if (phase_value_deg_ch0 - PHASE_STEP_DEG >= PHASE_MIN_DEG)
        {
            phase_value_deg_ch0 -= PHASE_STEP_DEG;
            Update_Channel_Output(0);
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH0_PHASE_MINUS);

            // 更新HMI显示
            HMI_Send_Int("page0.n4", phase_value_deg_ch0); // CH0相位显示
            HMI_Send_String("page0.information", "CH0 Phase decreased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH0 Min phase: 0°");
        }
    }

    // ========== CH1 相位控制命令 ==========
    else if (strcmp(command, "ch1_pha_plus") == 0)
    {
        if (phase_value_deg_ch1 + PHASE_STEP_DEG <= PHASE_MAX_DEG)
        {
            phase_value_deg_ch1 += PHASE_STEP_DEG;
            Update_Channel_Output(1);
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH1_PHASE_PLUS);

            // 更新HMI显示
            HMI_Send_Int("page0.n5", phase_value_deg_ch1); // CH1相位显示
            HMI_Send_String("page0.information", "CH1 Phase increased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH1 Max phase: 180°");
        }
    }
    else if (strcmp(command, "ch1_pha_minus") == 0)
    {
        if (phase_value_deg_ch1 - PHASE_STEP_DEG >= PHASE_MIN_DEG)
        {
            phase_value_deg_ch1 -= PHASE_STEP_DEG;
            Update_Channel_Output(1);
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH1_PHASE_MINUS);

            // 更新HMI显示
            HMI_Send_Int("page0.n5", phase_value_deg_ch1); // CH1相位显示
            HMI_Send_String("page0.information", "CH1 Phase decreased");
        }
        else
        {
            HMI_Send_String("page0.information", "CH1 Min phase: 0°");
        }
    }

    // ========== CH0 时延控制命令 ==========
    else if (strcmp(command, "ch0_time_plus") == 0)
    {
        if (time_delay_ns_ch0 + TIME_DELAY_STEP_NS <= TIME_DELAY_MAX_NS)
        {
            time_delay_ns_ch0 += TIME_DELAY_STEP_NS;
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH0_TIME_PLUS);

            // 更新CH0输出（重新计算相位）
            Update_CH0_With_Time_Delay();

            // 更新HMI显示
            HMI_Send_Int("page0.n6", time_delay_ns_ch0); // CH0时延显示
            HMI_Send_String("page0.information", "CH0 Time delay increased");

            // 调试输出（包含相对时延）
            my_printf(&huart1, "CH0 Time delay: %d ns (Phase: %.1f°)\r\n",
                      time_delay_ns_ch0,
                      Calculate_Phase_From_Delay(freq_value_ch0, time_delay_ns_ch0));
            Display_Relative_Delay_Info();
        }
        else
        {
            HMI_Send_String("page0.information", "CH0 Max time delay: 200ns");
        }
    }
    else if (strcmp(command, "ch0_time_minus") == 0)
    {
        if (time_delay_ns_ch0 - TIME_DELAY_STEP_NS >= TIME_DELAY_MIN_NS)
        {
            time_delay_ns_ch0 -= TIME_DELAY_STEP_NS;
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH0_TIME_MINUS);

            // 更新CH0输出（重新计算相位）
            Update_CH0_With_Time_Delay();

            // 更新HMI显示
            HMI_Send_Int("page0.n6", time_delay_ns_ch0); // CH0时延显示
            HMI_Send_String("page0.information", "CH0 Time delay decreased");

            // 调试输出（包含相对时延）
            my_printf(&huart1, "CH0 Time delay: %d ns (Phase: %.1f°)\r\n",
                      time_delay_ns_ch0,
                      Calculate_Phase_From_Delay(freq_value_ch0, time_delay_ns_ch0));
            Display_Relative_Delay_Info();
        }
        else
        {
            HMI_Send_String("page0.information", "CH0 Min time delay: 50ns");
        }
    }

    // ========== CH1 时延控制命令 ==========
    else if (strcmp(command, "ch1_time_plus") == 0)
    {
        // // ⭐⭐⭐ 添加调试输出
        // my_printf(&huart1, "Current: %d, Step: %d, Max: %d\r\n",
        //           time_delay_ns_ch1, TIME_DELAY_STEP_NS, TIME_DELAY_MAX_NS);

        if (time_delay_ns_ch1 + TIME_DELAY_STEP_NS <= TIME_DELAY_MAX_NS)
        {
            time_delay_ns_ch1 += TIME_DELAY_STEP_NS;
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH1_TIME_PLUS);

            // 更新CH1输出（重新计算相位）
            Update_CH1_With_Time_Delay();

            // 更新HMI显示
            HMI_Send_Int("page0.n8", time_delay_ns_ch1); // CH1时延显示
            HMI_Send_String("page0.information", "CH1 Time delay increased");

            // // 调试输出（包含相对时延）
            // my_printf(&huart1, "CH1 Time delay: %d ns (Phase: %.1f°)\r\n",
            //           time_delay_ns_ch1,
            //           Calculate_Phase_From_Delay(freq_value_ch1, time_delay_ns_ch1));
            // Display_Relative_Delay_Info();
        }
        else
        {
            // // ⭐⭐⭐ 添加调试输出
            // my_printf(&huart1, "Reached max limit: %d\r\n", TIME_DELAY_MAX_NS);
            HMI_Send_String("page0.information", "CH1 Max time delay: 200ns");
        }
    }
    else if (strcmp(command, "ch1_time_minus") == 0)
    {
        // // ⭐⭐⭐ 添加调试输出
        // my_printf(&huart1, "Current: %d, Step: %d, Min: %d\r\n",
        //           time_delay_ns_ch1, TIME_DELAY_STEP_NS, TIME_DELAY_MIN_NS);
        if (time_delay_ns_ch1 - TIME_DELAY_STEP_NS >= TIME_DELAY_MIN_NS)
        {
            time_delay_ns_ch1 -= TIME_DELAY_STEP_NS;
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH1_TIME_MINUS);

            // 更新CH1输出（重新计算相位）
            Update_CH1_With_Time_Delay();

            // 更新HMI显示
            HMI_Send_Int("page0.n8", time_delay_ns_ch1); // CH1时延显示
            HMI_Send_String("page0.information", "CH1 Time delay decreased");

            // // 调试输出（包含相对时延）
            // my_printf(&huart1, "CH1 Time delay: %d ns (Phase: %.1f°)\r\n",
            //           time_delay_ns_ch1,
            //           Calculate_Phase_From_Delay(freq_value_ch1, time_delay_ns_ch1));
            // Display_Relative_Delay_Info();
        }
        else
        {
            // // ⭐⭐⭐ 添加调试输出
            // my_printf(&huart1, "Reached min limit: %d\r\n", TIME_DELAY_MIN_NS);
            HMI_Send_String("page0.information", "CH1 Min time delay: 50ns");
        }
    }

    // ========== CH0 调制度控制命令 ==========
    else if (strcmp(command, "ch0_deepth_plus") == 0)
    {
        if (modulation_depth_ch0 + DEPTH_STEP_PERCENT <= DEPTH_MAX_PERCENT)
        {
            modulation_depth_ch0 += DEPTH_STEP_PERCENT;
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH0_DEPTH_PLUS);

            // 发送峰峰值信息给FPGA
            Send_Peak_Value_To_FPGA(0);

            // 更新HMI显示
            HMI_Send_Int("page1.n0", modulation_depth_ch0); // CH0调制度显示
            HMI_Send_String("page1.information", "CH0 Modulation depth increased");

            my_printf(&huart1, "CH0 Modulation depth: %d%%\r\n", modulation_depth_ch0);
        }
        else
        {
            HMI_Send_String("page1.information", "CH0 Max modulation depth: 100%");
        }
    }
    else if (strcmp(command, "ch0_deepth_minus") == 0)
    {
        if (modulation_depth_ch0 - DEPTH_STEP_PERCENT >= DEPTH_MIN_PERCENT)
        {
            modulation_depth_ch0 -= DEPTH_STEP_PERCENT;
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH0_DEPTH_MINUS);

            // 发送峰峰值信息给FPGA
            Send_Peak_Value_To_FPGA(0);

            // 更新HMI显示
            HMI_Send_Int("page1.n0", modulation_depth_ch0); // CH0调制度显示
            HMI_Send_String("page1.information", "CH0 Modulation depth decreased");

            my_printf(&huart1, "CH0 Modulation depth: %d%%\r\n", modulation_depth_ch0);
        }
        else
        {
            HMI_Send_String("page1.information", "CH0 Min modulation depth: 0%");
        }
    }

    // ========== CH1 调制度控制命令 ==========
    else if (strcmp(command, "ch1_deepth_plus") == 0)
    {
        if (modulation_depth_ch1 + DEPTH_STEP_PERCENT <= DEPTH_MAX_PERCENT)
        {
            modulation_depth_ch1 += DEPTH_STEP_PERCENT;
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH1_DEPTH_PLUS);

            // 发送峰峰值信息给FPGA
            Send_Peak_Value_To_FPGA(1);

            // 更新HMI显示
            HMI_Send_Int("page1.n1", modulation_depth_ch1); // CH1调制度显示
            HMI_Send_String("page1.information", "CH1 Modulation depth increased");

            my_printf(&huart1, "CH1 Modulation depth: %d%%\r\n", modulation_depth_ch1);
        }
        else
        {
            HMI_Send_String("page1.information", "CH1 Max modulation depth: 100%");
        }
    }
    else if (strcmp(command, "ch1_deepth_minus") == 0)
    {
        if (modulation_depth_ch1 - DEPTH_STEP_PERCENT >= DEPTH_MIN_PERCENT)
        {
            modulation_depth_ch1 -= DEPTH_STEP_PERCENT;
            Set_Parameter_Changed();

            // 发送FPGA命令
            Send_FPGA_Command(FPGA_CMD_CH1_DEPTH_MINUS);

            // 发送峰峰值信息给FPGA
            Send_Peak_Value_To_FPGA(1);

            // 更新HMI显示
            HMI_Send_Int("page1.n1", modulation_depth_ch1); // CH1调制度显示
            HMI_Send_String("page1.information", "CH1 Modulation depth decreased");

            my_printf(&huart1, "CH1 Modulation depth: %d%%\r\n", modulation_depth_ch1);
        }
        else
        {
            HMI_Send_String("page1.information", "CH1 Min modulation depth: 0%");
        }
    }

    // ========== 状态查询命令 ==========
    else if (strcmp(command, "test") == 0)
    {
        // 显示当前设置的参数值
        HMI_Send_String("page0.information", "Current parameters displayed");
        my_printf(&huart1, "CH0: Freq=%.1fMHz, Amp=%dmV, Phase=%d°, Delay=%dns\r\n",
                  freq_value_ch0 / 1000000.0f, amp_value_mv_ch0, phase_value_deg_ch0, time_delay_ns_ch0);
        my_printf(&huart1, "CH1: Freq=%.1fMHz, Amp=%dmV, Phase=%d°, Delay=%dns\r\n",
                  freq_value_ch1 / 1000000.0f, amp_value_mv_ch1, phase_value_deg_ch1, time_delay_ns_ch1);
        Display_Relative_Delay_Info();
    }

    // ========== 状态查询命令 ==========
    else if (strcmp(command, "status") == 0)
    {
        // 更新所有HMI显示
        HMI_Send_Float("page0.n0", freq_value_ch0 / 1000000.0f, 1); // CH0频率
        HMI_Send_Int("page0.n2", amp_value_mv_ch0);                 // CH0幅度
        HMI_Send_Int("page0.n4", phase_value_deg_ch0);              // CH0相位
        HMI_Send_Int("page0.n6", time_delay_ns_ch0);                // CH0时延

        HMI_Send_Float("page0.n1", freq_value_ch1 / 1000000.0f, 1); // CH1频率
        HMI_Send_Int("page0.n3", amp_value_mv_ch1);                 // CH1幅度
        HMI_Send_Int("page0.n5", phase_value_deg_ch1);              // CH1相位
        HMI_Send_Int("page0.n8", time_delay_ns_ch1);                // CH1时延

        // 更新调制度参数显示
        HMI_Send_Int("page1.n0", modulation_depth_ch0); // CH0调制度
        HMI_Send_Int("page1.n1", modulation_depth_ch1); // CH1调制度

        HMI_Send_String("page0.information", "Status updated");
    }
    else if (strcmp(command, "ch0_status") == 0)
    {
        // 更新CH0显示
        HMI_Send_Float("page0.n0", freq_value_ch0 / 1000000.0f, 1);
        HMI_Send_Int("page0.n2", amp_value_mv_ch0);
        HMI_Send_Int("page0.n4", phase_value_deg_ch0);
        HMI_Send_Int("page0.n6", time_delay_ns_ch0);
        HMI_Send_Int("page1.n0", modulation_depth_ch0);

        HMI_Send_String("page0.information", "CH0 status updated");
    }
    else if (strcmp(command, "ch1_status") == 0)
    {
        // 更新CH1显示
        HMI_Send_Float("page0.n1", freq_value_ch1 / 1000000.0f, 1);
        HMI_Send_Int("page0.n3", amp_value_mv_ch1);
        HMI_Send_Int("page0.n5", phase_value_deg_ch1);
        HMI_Send_Int("page0.n8", time_delay_ns_ch1);
        HMI_Send_Int("page1.n1", modulation_depth_ch1);

        HMI_Send_String("page0.information", "CH1 status updated");
    }
    else if (strcmp(command, "help") == 0)
    {
        HMI_Debug_Print("Commands: ch0/ch1_freq/amp/pha/time_plus/minus, ch0/ch1_deepth_plus/minus, test, status, help");
    }
    else
    {
        HMI_Send_String("page0.information", "Unknown command. Type 'help'");
    }
}

/**
 * @brief 串口接收中断回调函数
 * @param huart 串口句柄
 */
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART1) // USART1的接收中断
    {
        if (rxIndex1 < RX_BUFFER_SIZE - 1)
        {
            // 如果接收到换行符并且前一个字符是回车符，标记命令接收完成
            if (rxTemp1 == '\n' && rxIndex1 > 0 && rxBuffer1[rxIndex1 - 1] == '\r')
            {
                commandReceived1 = 1;           // 设置命令接收标志
                rxBuffer1[rxIndex1 - 1] = '\0'; // 添加字符串结束符
            }
            else if (rxTemp1 == '\n' || rxTemp1 == '\r') // 处理单独的换行符或回车符
            {
                commandReceived1 = 1;       // 设置命令接收标志
                rxBuffer1[rxIndex1] = '\0'; // 添加字符串结束符
            }
            else
            {
                rxBuffer1[rxIndex1++] = rxTemp1; // 保存接收到的数据
            }
        }
        else
        {
            rxIndex1 = 0; // 缓冲区溢出，重置索引
        }
        HAL_UART_Receive_IT(&huart1, &rxTemp1, 1); // 再次启动接收中断
    }
    else if (huart->Instance == USART3) // USART3的接收中断 (FPGA返回数据)
    {
        if (rxIndex3 < RX_BUFFER_SIZE - 1)
        {
            // 如果接收到换行符并且前一个字符是回车符，标记命令接收完成
            if (rxTemp3 == '\n' && rxIndex3 > 0 && rxBuffer3[rxIndex3 - 1] == '\r')
            {
                commandReceived3 = 1;           // 设置命令接收标志
                rxBuffer3[rxIndex3 - 1] = '\0'; // 添加字符串结束符
            }
            else if (rxTemp3 == '\n' || rxTemp3 == '\r') // 处理单独的换行符或回车符
            {
                commandReceived3 = 1;       // 设置命令接收标志
                rxBuffer3[rxIndex3] = '\0'; // 添加字符串结束符
            }
            else
            {
                rxBuffer3[rxIndex3++] = rxTemp3; // 保存接收到的数据
            }
        }
        else
        {
            rxIndex3 = 0; // 缓冲区溢出，重置索引
        }
        HAL_UART_Receive_IT(&huart3, &rxTemp3, 1); // 再次启动接收中断
    }
}

/**
 * @brief 初始化串口1中断接收
 */
void USART1_Init_IT(void)
{
    rxIndex1 = 0;
    commandReceived1 = 0;
    HAL_UART_Receive_IT(&huart1, &rxTemp1, 1);
}

/**
 * @brief 初始化串口3中断接收
 */
void USART3_Init_IT(void)
{
    rxIndex3 = 0;
    commandReceived3 = 0;
    HAL_UART_Receive_IT(&huart3, &rxTemp3, 1);
}

/**
 * @brief 检查并处理串口1命令（在主循环中调用）
 */
void Check_UART1_Command(void)
{
    if (commandReceived1)
    {
        Parse_Command_UART1((char *)rxBuffer1);
        commandReceived1 = 0; // 清除命令接收标志
        rxIndex1 = 0;         // 重置接收索引
    }
}

/**
 * @brief 检查并处理串口3命令（在主循环中调用）
 */
void Check_UART3_Command(void)
{
    if (commandReceived3)
    {
        // 处理FPGA返回的数据
        my_printf(&huart1, "FPGA Response: %s\r\n", rxBuffer3);
        HMI_Send_String("page0.information", "FPGA response received");

        commandReceived3 = 0; // 清除命令接收标志
        rxIndex3 = 0;         // 重置接收索引
    }
}

/**
 * @brief 获取CH0调制度
 * @return CH0调制度百分比
 */
uint8_t Get_CH0_Modulation_Depth(void)
{
    return modulation_depth_ch0;
}

/**
 * @brief 获取CH1调制度
 * @return CH1调制度百分比
 */
uint8_t Get_CH1_Modulation_Depth(void)
{
    return modulation_depth_ch1;
}

/**
 * @brief 获取CH0时延
 * @return CH0时延纳秒
 */
uint16_t Get_CH0_Time_Delay(void)
{
    return time_delay_ns_ch0;
}

/**
 * @brief 获取CH1时延
 * @return CH1时延纳秒
 */
uint16_t Get_CH1_Time_Delay(void)
{
    return time_delay_ns_ch1;
}
