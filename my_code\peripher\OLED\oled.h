#ifndef __OLED_H
#define __OLED_H

#include "main.h" // CubeMX生成的main.h，包含了HAL库
#include "stdlib.h"

// 数据类型定义（如果main.h中没有的话）
#ifndef u8
typedef unsigned char u8;
typedef unsigned short u16;
typedef unsigned int u32;
#endif

//-----------------OLED端口定义----------------
// 使用HAL库的GPIO操作函数，连接到PC11和PC12

#define OLED_SCL_Clr()                                                         \
  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_12, GPIO_PIN_RESET) // SCL -> PC12
#define OLED_SCL_Set() HAL_GPIO_WritePin(GPIOC, GPIO_PIN_12, GPIO_PIN_SET)

#define OLED_SDA_Clr()                                                         \
  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_11, GPIO_PIN_RESET) // SDA -> PC11
#define OLED_SDA_Set() HAL_GPIO_WritePin(GPIOC, GPIO_PIN_11, GPIO_PIN_SET)

// 注释掉RES引脚定义，因为你的OLED模块只有4个引脚（VCC、GND、SCL、SDA）
// #define OLED_RES_Clr() HAL_GPIO_WritePin(GPIOD, GPIO_PIN_4, GPIO_PIN_RESET)
// #define OLED_RES_Set() HAL_GPIO_WritePin(GPIOD, GPIO_PIN_4, GPIO_PIN_SET)

#define OLED_CMD 0  //写命令
#define OLED_DATA 1 //写数据

// 函数声明
void OLED_ClearPoint(u8 x, u8 y);
void OLED_ColorTurn(u8 i);
void OLED_DisplayTurn(u8 i);
void I2C_Start(void);
void I2C_Stop(void);
void I2C_WaitAck(void);
void Send_Byte(u8 dat);
void OLED_WR_Byte(u8 dat, u8 mode);
void OLED_DisPlay_On(void);
void OLED_DisPlay_Off(void);
void OLED_Refresh(void);
void OLED_Clear(void);
void OLED_DrawPoint(u8 x, u8 y, u8 t);
void OLED_DrawLine(u8 x1, u8 y1, u8 x2, u8 y2, u8 mode);
void OLED_DrawCircle(u8 x, u8 y, u8 r);
void OLED_ShowChar(u8 x, u8 y, u8 chr, u8 size1, u8 mode);
void OLED_ShowChar6x8(u8 x, u8 y, u8 chr, u8 mode);
void OLED_ShowString(u8 x, u8 y, u8 *chr, u8 size1, u8 mode);
void OLED_ShowNum(u8 x, u8 y, u32 num, u8 len, u8 size1, u8 mode);
void OLED_ShowChinese(u8 x, u8 y, u8 num, u8 size1, u8 mode);
void OLED_ScrollDisplay(u8 num, u8 space, u8 mode);
void OLED_ShowPicture(u8 x, u8 y, u8 sizex, u8 sizey, u8 BMP[], u8 mode);
void OLED_Init(void);

#endif
