#include "main.h"
#include "AD9959.h"
#include "test_functions.h"
#include "usart.h"
#include "string.h"
#include "stdio.h"
#include "stdarg.h"

// =================================================================
//                      简化的AD9959测试函数
// =================================================================

// 简单的串口输出函数
void simple_uart_print(const char* str)
{
    HAL_UART_Transmit(&huart1, (uint8_t*)str, strlen(str), 1000);
}

// 简单的格式化输出函数
void simple_printf(const char* format, ...)
{
    char buffer[256];
    va_list args;
    va_start(args, format);
    vsnprintf(buffer, sizeof(buffer), format, args);
    va_end(args);
    simple_uart_print(buffer);
}

/**
 * @brief 直接设置频率和幅度 - 最简单的控制函数
 * @param channel 通道号 (0或1)
 * @param freq_mhz 频率 (MHz)
 * @param amp_mv 幅度 (mV)
 */
void Set_Freq_Amp(uint8_t channel, float freq_mhz, uint16_t amp_mv)
{
    uint32_t freq_hz = (uint32_t)(freq_mhz * 1000000);
    uint16_t ad9959_amp = (amp_mv * 1023) / 250; // 转换到0-1023范围
    if (ad9959_amp > 1023) ad9959_amp = 1023;
    
    AD9959_Single_Output(channel, freq_hz, 0.0f, ad9959_amp);
    simple_printf("CH%d: %.1f MHz, %u mV\r\n", channel, freq_mhz, amp_mv);
}

/**
 * @brief 快速设置CH0
 */
void Set_CH0(float freq_mhz, uint16_t amp_mv)
{
    Set_Freq_Amp(0, freq_mhz, amp_mv);
}

/**
 * @brief 快速设置CH1
 */
void Set_CH1(float freq_mhz, uint16_t amp_mv)
{
    Set_Freq_Amp(1, freq_mhz, amp_mv);
}

/**
 * @brief 测试函数1：频率扫描测试
 */
void Test1_Frequency_Sweep(void)
{
    simple_uart_print("\r\n=== 测试1：频率扫描 ===\r\n");

    for (float freq = 30.0f; freq <= 40.0f; freq += 1.0f)
    {
        Set_CH0(freq, 125); // CH0扫描30-40MHz，固定125mV
        HAL_Delay(500);
    }
    simple_uart_print("频率扫描测试完成\r\n");
}

/**
 * @brief 测试函数2：幅度扫描测试
 */
void Test2_Amplitude_Sweep(void)
{
    simple_uart_print("\r\n=== 测试2：幅度扫描 ===\r\n");

    uint16_t amps[] = {25, 50, 75, 100, 125, 150, 175, 200, 225, 250};

    for (int i = 0; i < 10; i++)
    {
        Set_CH0(35.0f, amps[i]); // CH0固定35MHz，扫描幅度
        HAL_Delay(500);
    }
    simple_uart_print("幅度扫描测试完成\r\n");
}

/**
 * @brief 测试函数3：双通道对比测试
 */
void Test3_Dual_Channel(void)
{
    simple_uart_print("\r\n=== 测试3：双通道对比 ===\r\n");

    // 设置不同的频率和幅度组合
    struct {
        float freq0, freq1;
        uint16_t amp0, amp1;
    } test_cases[] = {
        {30.0f, 35.0f, 100, 150},
        {32.0f, 38.0f, 125, 125},
        {35.0f, 35.0f, 75, 200},
        {40.0f, 30.0f, 250, 50}
    };

    for (int i = 0; i < 4; i++)
    {
        Set_CH0(test_cases[i].freq0, test_cases[i].amp0);
        Set_CH1(test_cases[i].freq1, test_cases[i].amp1);
        simple_printf("测试组合 %d 完成\r\n", i+1);
        HAL_Delay(1000);
    }
    simple_uart_print("双通道对比测试完成\r\n");
}

/**
 * @brief 测试函数4：快速切换测试
 */
void Test4_Fast_Switch(void)
{
    simple_uart_print("\r\n=== 测试4：快速切换 ===\r\n");

    for (int i = 0; i < 20; i++)
    {
        if (i % 2 == 0)
        {
            Set_CH0(35.0f, 125);
            Set_CH1(32.0f, 100);
        }
        else
        {
            Set_CH0(38.0f, 200);
            Set_CH1(30.0f, 75);
        }
        HAL_Delay(200); // 快速切换
    }
    simple_uart_print("快速切换测试完成\r\n");
}

/**
 * @brief 运行所有测试
 */
void Run_All_Tests(void)
{
    simple_uart_print("\r\n========== 开始所有测试 ==========\r\n");

    Test1_Frequency_Sweep();
    HAL_Delay(1000);

    Test2_Amplitude_Sweep();
    HAL_Delay(1000);

    Test3_Dual_Channel();
    HAL_Delay(1000);

    Test4_Fast_Switch();

    simple_uart_print("\r\n========== 所有测试完成 ==========\r\n");

    // 恢复默认设置
    Set_CH0(35.0f, 125);
    Set_CH1(32.0f, 100);
    simple_uart_print("已恢复默认设置\r\n");
}

// =================================================================
//                      便捷的预设函数
// =================================================================

/**
 * @brief 预设1：标准测试信号
 */
void Preset1_Standard(void)
{
    Set_CH0(35.0f, 125); // 35MHz, 125mV
    Set_CH1(35.0f, 125); // 35MHz, 125mV
    simple_uart_print("预设1：标准测试信号已设置\r\n");
}

/**
 * @brief 预设2：高频低幅
 */
void Preset2_HighFreq_LowAmp(void)
{
    Set_CH0(40.0f, 50);  // 40MHz, 50mV
    Set_CH1(38.0f, 75);  // 38MHz, 75mV
    simple_uart_print("预设2：高频低幅已设置\r\n");
}

/**
 * @brief 预设3：低频高幅
 */
void Preset3_LowFreq_HighAmp(void)
{
    Set_CH0(30.0f, 200); // 30MHz, 200mV
    Set_CH1(32.0f, 250); // 32MHz, 250mV
    simple_uart_print("预设3：低频高幅已设置\r\n");
}

/**
 * @brief 预设4：频率差测试
 */
void Preset4_FreqDiff(void)
{
    Set_CH0(35.0f, 125); // 35MHz, 125mV
    Set_CH1(30.0f, 125); // 30MHz, 125mV (5MHz差)
    simple_uart_print("预设4：频率差测试已设置 (5MHz差)\r\n");
}

/**
 * @brief 预设5：幅度差测试
 */
void Preset5_AmpDiff(void)
{
    Set_CH0(35.0f, 200); // 35MHz, 200mV
    Set_CH1(35.0f, 50);  // 35MHz, 50mV (150mV差)
    simple_uart_print("预设5：幅度差测试已设置 (150mV差)\r\n");
}
