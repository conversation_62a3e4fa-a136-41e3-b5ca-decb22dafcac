#ifndef __DELAY_H
#define __DELAY_H
#include "main.h"
#include "commond_init.h"

//////////////////////////////////////////////////////////////////////////////////
//是否支持OS系统
#define SYSTEM_SUPPORT_OS 0 //定义系统文件夹是否支持OS
//支持OS时，调用delay_ms会调用OS的延时函数
//不支持时就是普通的延时
//默认为0,不支持OS
//如果使用OS,设为1

//如果使用OS,需要包含对应的OS头文件
#if SYSTEM_SUPPORT_OS
//支持OS时的一些函数声明
void delay_osschedlock(void);   //锁定OS任务调度
void delay_osschedunlock(void); //解锁OS任务调度
void delay_ostimedly(u32 ticks); // OS延时
#endif

//延时函数声明
void delay_init(u8 SYSCLK); //延时函数初始化
void delay_ms(u16 nms); // ms级延时
void delay_us(u32 nus); // us级延时

#if !SYSTEM_SUPPORT_OS //不使用OS时才有这个函数
void delay_xms(u16 nms); // xms延时,仅在不使用OS时有效
#endif

#endif
