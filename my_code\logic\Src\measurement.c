#include "measurement.h"
#include "adc.h"        // 需要用到 hadc1
#include "bsp_system.h" // 需要用到系统状态和标志位
#include "my_hmi.h"     // 需要更新HMI显示

// =================================================================
//                  私有宏定义 (仅本文件使用)
// =================================================================
#define ADC_BUFFER_SIZE 64
#define PP_FILTER_BUFFER_SIZE 8

// =================================================================
//                  静态全局变量 (仅本文件使用)
// =================================================================

// ADC原始数据缓冲区
static uint16_t adc_buff[ADC_BUFFER_SIZE];

// 用于滑动平均滤波的缓冲区
static float pp_buffer[PP_FILTER_BUFFER_SIZE] = {0};
static uint8_t pp_buffer_index = 0;
static uint8_t pp_sample_count = 0;

// 测量结果
static float measured_amplitude = 0.0f;

// =================================================================
//                  私有函数 (仅本文件使用)
// =================================================================

/**
 * @brief 计算ADC数据的平均电压值
 * @param adc_data ADC数据指针
 * @param length 数据长度
 * @return float 平均电压 (V)
 */
static float calculate_average_voltage(uint16_t *adc_data, uint16_t length)
{
    uint32_t sum = 0;
    for (uint16_t i = 0; i < length; i++)
    {
        sum += adc_data[i];
    }
    // (sum / length) 是ADC平均值, * (3.3 / 4095.0) 转换为电压
    return (float)sum / (float)length * 3.3f / 4095.0f;
}

// =================================================================
//                  公有函数 (在.h中声明的接口实现)
// =================================================================

void Measurement_Init(void)
{
    // 初始化变量
    pp_buffer_index = 0;
    pp_sample_count = 0;
    measured_amplitude = 0.0f;

    // 如果需要，可以在这里清零缓冲区
    // memset(adc_buff, 0, sizeof(adc_buff));
    // memset(pp_buffer, 0, sizeof(pp_buffer));
}

void Measurement_Enable(void)
{
    // 从 bsp_system.h 获取 measurement_enable 标志位
    extern volatile uint8_t measurement_enable;
    extern volatile uint8_t adc_processing_flag;

    measurement_enable = 1;
    if (!adc_processing_flag)
    {
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_buff, ADC_BUFFER_SIZE);
    }
}

void Measurement_Disable(void)
{
    extern volatile uint8_t measurement_enable;
    measurement_enable = 0;
    HAL_ADC_Stop_DMA(&hadc1);
}

void Measurement_Process_DC(void)
{
    extern volatile uint8_t adc_processing_flag;
    extern volatile uint8_t measurement_enable;
    extern uint32_t last_measurement_time; // 从bsp_system.c获取

    adc_processing_flag = 1;
    HAL_ADC_Stop_DMA(&hadc1);

    // 计算平均电压
    float average_voltage = calculate_average_voltage(adc_buff, ADC_BUFFER_SIZE);

    // 滑动平均滤波
    pp_buffer[pp_buffer_index] = average_voltage;
    pp_buffer_index = (pp_buffer_index + 1) % PP_FILTER_BUFFER_SIZE;
    if (pp_sample_count < PP_FILTER_BUFFER_SIZE)
    {
        pp_sample_count++;
    }

    float avg_sum = 0;
    for (int i = 0; i < pp_sample_count; i++)
    {
        avg_sum += pp_buffer[i];
    }
    float final_voltage = avg_sum / (float)pp_sample_count;

    // 更新最终测量结果 (单位：mV)
    measured_amplitude = final_voltage * 1000.0f;

    // 更新HMI显示
    HMI_Send_Int("page0.n7", (uint16_t)measured_amplitude);

    if (measurement_enable)
    {
        HAL_ADC_Start_DMA(&hadc1, (uint32_t *)adc_buff, ADC_BUFFER_SIZE);
    }

    adc_processing_flag = 0;
    last_measurement_time = HAL_GetTick();
}

void Measurement_Reset_Filter(void)
{
    pp_sample_count = 0;
    pp_buffer_index = 0;
}

float Measurement_Get_Amplitude(void)
{
    return measured_amplitude;
}

// =================================================================
//                      回调函数实现
// =================================================================

void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc)
{
    // 从 bsp_system.h 获取外部变量和状态
    extern volatile uint8_t measurement_enable;
    extern volatile uint8_t adc_processing_flag;
    extern volatile uint8_t fft_calculate_flag; // 假设这个是你用于触发的标志
    extern SystemState_t system_state;

    if (hadc->Instance == ADC1 && measurement_enable)
    {
        static uint32_t dma_complete_counter = 0;
        dma_complete_counter++;

        // 根据采样率调整触发频率，约2秒更新一次 (这个逻辑可以根据你的需求保留或修改)
        if (dma_complete_counter >= 20)
        {
            if (!adc_processing_flag && system_state == SYSTEM_IDLE)
            {
                fft_calculate_flag = 1; // 设置标志，让主循环中的状态机来处理
            }
            dma_complete_counter = 0;
        }
    }
}
