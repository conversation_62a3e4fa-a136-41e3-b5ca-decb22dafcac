/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2025 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "AD9959.h"      // 包含AD9959驱动头文件
#include "test_functions.h" // 包含测试函数头文件
#include "stdio.h"
#include "math.h"
#include "usart.h"       // 包含串口基础头文件
#include "string.h"      // 包含字符串函数
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

// =================================================================
//                      简化的AD9959控制函数
// =================================================================

/**
 * @brief 简单设置单通道频率和幅度
 * @param channel 通道号 (0或1)
 * @param freq_hz 频率 (Hz)
 * @param amp_mv 幅度 (mV)
 */
void Simple_Set_Channel(uint8_t channel, uint32_t freq_hz, uint16_t amp_mv)
{
    // 转换幅度值到AD9959格式 (0-1023)
    uint16_t ad9959_amp = (amp_mv * 1023) / 250; // 假设最大250mV对应1023
    if (ad9959_amp > 1023) ad9959_amp = 1023;

    // 设置通道输出
    AD9959_Single_Output(channel, freq_hz, 0.0f, ad9959_amp);

    // 简单的串口输出
    char buffer[100];
    snprintf(buffer, sizeof(buffer), "CH%d: %.1f MHz, %u mV\r\n",
              channel, freq_hz / 1000000.0f, amp_mv);
    HAL_UART_Transmit(&huart1, (uint8_t*)buffer, strlen(buffer), 1000);
}

/**
 * @brief 快速测试演示函数
 */
void Quick_Test_Demo(void)
{
    char msg[] = "Quick Test Demo: Setting CH0=35MHz/125mV, CH1=32MHz/100mV\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);

    Set_CH0(35.0f, 125);
    HAL_Delay(1000);
    Set_CH1(32.0f, 100);
    HAL_Delay(1000);

    char done_msg[] = "Demo completed!\r\n";
    HAL_UART_Transmit(&huart1, (uint8_t*)done_msg, strlen(done_msg), 1000);
}

/**
 * @brief 快速测试函数 - 设置不同频率
 */
void Test_Frequency_Control(void)
{
    my_printf(&huart1, "\r\n=== 频率控制测试 ===\r\n");

    uint32_t test_freqs[] = {30000000, 32000000, 35000000, 38000000, 40000000}; // 30-40MHz

    for (int i = 0; i < 5; i++)
    {
        Simple_Set_Channel(0, test_freqs[i], 125); // CH0固定125mV
        HAL_Delay(1000);
    }
}

/**
 * @brief 快速测试函数 - 设置不同幅度
 */
void Test_Amplitude_Control(void)
{
    my_printf(&huart1, "\r\n=== 幅度控制测试 ===\r\n");

    uint16_t test_amps[] = {25, 50, 100, 150, 200, 250}; // 25-250mV

    for (int i = 0; i < 6; i++)
    {
        Simple_Set_Channel(0, 35000000, test_amps[i]); // CH0固定35MHz
        HAL_Delay(1000);
    }
}

/**
 * @brief 双通道同时设置
 */
void Set_Dual_Channel_Simple(uint32_t freq0, uint16_t amp0, uint32_t freq1, uint16_t amp1)
{
    Simple_Set_Channel(0, freq0, amp0);
    Simple_Set_Channel(1, freq1, amp1);
}

/**
 * @brief 快速测试 - 可以直接在main函数中调用
 */
void Quick_Test_Demo(void)
{
    my_printf(&huart1, "\r\n=== 快速测试演示 ===\r\n");

    // 测试不同频率
    my_printf(&huart1, "测试不同频率...\r\n");
    Set_CH0(30.0f, 125);
    HAL_Delay(1000);
    Set_CH0(35.0f, 125);
    HAL_Delay(1000);
    Set_CH0(40.0f, 125);
    HAL_Delay(1000);

    // 测试不同幅度
    my_printf(&huart1, "测试不同幅度...\r\n");
    Set_CH0(35.0f, 50);
    HAL_Delay(1000);
    Set_CH0(35.0f, 125);
    HAL_Delay(1000);
    Set_CH0(35.0f, 200);
    HAL_Delay(1000);

    // 测试双通道
    my_printf(&huart1, "测试双通道...\r\n");
    Set_CH0(35.0f, 125);
    Set_CH1(32.0f, 100);
    HAL_Delay(2000);

    my_printf(&huart1, "快速测试完成!\r\n");
}
/* USER CODE END 0 */

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick.
   */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_USART2_UART_Init();
  MX_USART3_UART_Init();
  MX_ADC1_Init();
  MX_TIM2_Init();
  /* USER CODE BEGIN 2 */



  /*简化初始化代码*/
  // 初始化AD9959
  AD9959_Init();

  // 简单的串口输出函数
  char msg[] = "AD9959 System initialized!\r\n";
  HAL_UART_Transmit(&huart1, (uint8_t*)msg, strlen(msg), 1000);

  // 设置初始参数：CH0=35MHz/125mV, CH1=32MHz/100mV
  Set_CH0(35.0f, 125);
  Set_CH1(32.0f, 100);

  // 简单的系统就绪消息
  char ready_msg[] = "\r\nSystem Ready! Test functions available.\r\n";
  HAL_UART_Transmit(&huart1, (uint8_t*)ready_msg, strlen(ready_msg), 1000);

  // 运行一个简单的演示
  HAL_Delay(2000);
  char demo_msg[] = "\r\nStarting demo...\r\n";
  HAL_UART_Transmit(&huart1, (uint8_t*)demo_msg, strlen(demo_msg), 1000);
  Quick_Test_Demo(); // 运行快速测试演示



  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    // 简化的主循环 - 可以在这里添加你的测试代码

    // 示例1：每10秒运行一次幅度扫描测试
    // Test2_Amplitude_Sweep();
    // HAL_Delay(10000);

    // 示例2：循环切换不同预设
    // Preset1_Standard();
    // HAL_Delay(3000);
    // Preset2_HighFreq_LowAmp();
    // HAL_Delay(3000);
    // Preset3_LowFreq_HighAmp();
    // HAL_Delay(3000);

    // 示例3：手动设置参数
    // Set_CH0(35.0f, 125);
    // Set_CH1(30.0f, 200);
    // HAL_Delay(2000);
    // Set_CH0(40.0f, 50);
    // Set_CH1(38.0f, 75);
    // HAL_Delay(2000);

    HAL_Delay(100); // 默认延时，防止CPU空转

    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
   */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
   * in the RCC_OscInitTypeDef structure.
   */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
   */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK |
                                RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line
     number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
