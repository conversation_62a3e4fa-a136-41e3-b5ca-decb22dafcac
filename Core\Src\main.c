/* USER CODE BEGIN Header */
/**
 ******************************************************************************
 * @file           : main.c
 * @brief          : Main program body
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2025 STMicroelectronics.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 ******************************************************************************
 */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "bsp_system.h"  // 包含系统头文件
#include "my_usart.h"    // 包含串口头文件
#include "my_hmi.h"      // 包含HMI头文件
#include "AD9959.h"      // 包含AD9959驱动头文件
#include "measurement.h" // 包含测量模块头文件
#include "stdio.h"       // 新增
#include "math.h"        // 新增
#include "oled.h"
// #include "arm_math.h" // 新增
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */

/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */

/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */
// 在初始化函数中调用

/**
 * @brief 同步更新双通道相位差示例
 * @param phase_diff 相位差（度）
 */
void Demo_Sync_Phase_Control(float phase_diff)
{
  // 启用同步更新模式
  Enable_Sync_Update();

  // 同步设置两个通道的参数
  // CH0: 35MHz, 0°, 125mV
  // CH1: 35MHz, phase_diff°, 125mV
  Update_Dual_Channel_Sync(
      35000000, 0.0f, 125,      // CH0参数
      35000000, phase_diff, 125 // CH1参数
  );

  // 打印调试信息
  my_printf(&huart1, "Synchronized update: CH0=0°, CH1=%.1f°\r\n", phase_diff);
}

/**
 * @brief 测试同步控制功能
 */
void Test_Sync_Control(void)
{
  my_printf(&huart1, "\r\n=== Testing Sync Control ===\r\n");

  // 测试不同相位差的同步控制
  float phase_diffs[] = {0, 30, 60, 90, 120, 180};

  for (int i = 0; i < 6; i++)
  {
    Demo_Sync_Phase_Control(phase_diffs[i]);
    HAL_Delay(1000); // 等待1秒观察结果
  }

  // 禁用同步模式，恢复正常模式
  Disable_Sync_Update();
  my_printf(&huart1, "Sync control test completed, back to normal mode\r\n");
}

/**
 * @brief 精确相位同步设置函数
 * @param freq 频率(Hz)
 * @param phase0 CH0相位(度)
 * @param phase1 CH1相位(度)
 * @param amp0 CH0幅度(mV)
 * @param amp1 CH1幅度(mV)
 */
void Set_Precise_Dual_Channel(uint32_t freq, float phase0, float phase1,
                              uint16_t amp0, uint16_t amp1)
{
  // 启用同步模式
  Enable_Sync_Update();

  // 同步设置参数
  Update_Dual_Channel_Sync(freq, phase0, amp0, freq, phase1, amp1);

  my_printf(&huart1, "Precise sync: CH0(%.1fMHz, %.1f°, %umV), CH1(%.1fMHz, %.1f°, %umV)\r\n",
            freq / 1000000.0f, phase0, amp0, freq / 1000000.0f, phase1, amp1);
}
void System_Init_Time_Delay(void)
{
  time_delay_ns_ch1 = 50;       // 初始化为最小时延50ns
  Update_CH1_With_Time_Delay(); // 应用时延设置
}

// 在System_Init_Time_Delay函数后添加以下初始化：
void System_Init_Sync_Control(void)
{
  // 默认禁用同步模式
  Disable_Sync_Update();

  my_printf(&huart1, "Sync control system initialized\r\n");
  my_printf(&huart1, "Use 'sync_test' command to test sync functionality\r\n");
}
/* USER CODE END 0 */

/**
 * @brief  The application entry point.
 * @retval int
 */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick.
   */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_USART1_UART_Init();
  MX_USART2_UART_Init();
  MX_USART3_UART_Init();
  MX_ADC1_Init();
  MX_TIM2_Init();
  /* USER CODE BEGIN 2 */

  // // OLED初始化
  // OLED_Init();

  // // 清屏
  // OLED_Clear();

  // // 显示系统初始化消息
  // OLED_ShowString(0, 0, (u8 *)"STM32F407", 16, 1);
  // OLED_ShowString(0, 20, (u8 *)"Initializing...", 12, 1);
  // OLED_Refresh();

  /*正式代码*/
  HAL_UART_Receive_IT(&huart1, &rxTemp1, 1);

  AD9959_Init();
  // my_printf(&huart1, "AD9959 Dual Channel System initialized!\r\n");

  // 初始化CH0和CH1为相同的频率和幅度默认值，但相位差30°且独立控制
  Update_Channel_Output(0);
  Update_Channel_Output(1);
  System_Init_Time_Delay();
  // 初始化同步控制系统
  System_Init_Sync_Control();
  // my_printf(&huart1, "CH0: %.1f MHz, %u° phase, %u mV amplitude\r\n",
  //           freq_value_ch0 / 1000000.0f, phase_value_deg_ch0,
  //           amp_value_mv_ch0);
  // my_printf(&huart1, "CH1: %.1f MHz, %u° phase, %u mV amplitude\r\n",
  //           freq_value_ch1 / 1000000.0f, phase_value_deg_ch1,
  //           amp_value_mv_ch1);
  // my_printf(&huart1, "Type 'help' for available commands\r\n");

  // 初始化HMI显示 - 显示CH0参数
  HMI_Send_Float("page0.n0", freq_value_ch0 / 1000000.0f,
                 1);                             // CH0频率显示 (n0)，保留1位小数
  HMI_Send_Int("page0.n2", amp_value_mv_ch0);    // CH0幅度显示 (n2)
  HMI_Send_Int("page0.n4", phase_value_deg_ch0); // CH0相位显示 (n4)

  // 初始化HMI显示 - 显示CH1参数
  HMI_Send_Float("page0.n1", freq_value_ch1 / 1000000.0f,
                 1);                             // CH1频率显示 (n1)，保留1位小数
  HMI_Send_Int("page0.n3", amp_value_mv_ch1);    // CH1幅度显示 (n3)
  HMI_Send_Int("page0.n5", phase_value_deg_ch1); // CH1相位显示 (n5)

  // ⭐⭐⭐ 添加这两行：初始化时延显示
  HMI_Send_Int("page0.n6", time_delay_ns_ch0); // CH0时延显示 (n6)
  HMI_Send_Int("page0.n8", time_delay_ns_ch1); // CH1时延显示 (n8)

  // 显示系统就绪信息
  HMI_Debug_Print("System Ready!");
  HMI_Debug_Print("Type 'help' for available commands");

  // 初始化FFT相关
  HAL_TIM_Base_Start(&htim2);

  // 延迟启动ADC，等待系统稳定
  HAL_Delay(1000);
  Measurement_Enable(); // 使用新的接口函数启动测量

  // 在HMI显示初始化后添加测量结果显示
  HMI_Send_Int("page0.n7", 0); // 测量幅度显示 (假设n7控件)

  // 发送初始化完成消息给FPGA
  Send_FPGA_Command("INIT");
  HAL_Delay(100);

  // // 系统初始化完成后，更新OLED显示
  // OLED_Clear();
  // OLED_ShowString(0, 0, (u8 *)"STM32F407", 16, 1);
  // OLED_ShowString(0, 20, (u8 *)"System Init", 12, 1);
  // OLED_ShowString(0, 35, (u8 *)"OK!", 16, 1);

  // // 显示当前状态信息
  // OLED_ShowString(0, 55, (u8 *)"Ready", 8, 1);
  // OLED_Refresh();

  // // 延迟2秒显示初始化完成消息
  // HAL_Delay(2000);

  // // 然后显示正常的工作界面
  // OLED_Clear();
  // OLED_ShowString(0, 0, (u8 *)"CH0:", 12, 1);
  // OLED_ShowString(60, 0, (u8 *)"MHz", 8, 1);
  // OLED_ShowString(0, 15, (u8 *)"CH1:", 12, 1);
  // OLED_ShowString(60, 15, (u8 *)"MHz", 8, 1);
  // OLED_ShowString(0, 30, (u8 *)"Amp:", 8, 1);
  // OLED_ShowString(60, 30, (u8 *)"mV", 8, 1);
  // OLED_ShowString(0, 45, (u8 *)"Status: OK", 8, 1);
  // OLED_Refresh();

  /*第一版代码
  AD9959_Init();
  my_printf(&huart1, "System initialized!\r\n");

  // 使用新的初始值：35MHz频率，125mV幅度，90°相位
  u16 initial_amp = Convert_mV_to_AD9959_Amp(amp_value_mv);
  // 参数说明：通道号，频率(Hz)，相位(度)，幅度(0-1023)
  AD9959_Single_Output(0, freq_value, phase_value_deg, initial_amp);
  AD9959_IO_UpDate();

  my_printf(&huart1, "Channel 0: %.1f MHz, %u° phase, %u mV amplitude\r\n",
            freq_value / 1000000.0f, phase_value_deg, amp_value_mv);
  my_printf(&huart1, "Ready for commands: freq_plus, freq_minus, amp_plus,
  amp_minus, pha_plus, pha_minus, status\r\n");
  */

  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    Process_System_State(); // 处理系统状态机
                            // 如果启用了同步模式，定期检查同步更新
    if (sync_update_enable)
    {
      Check_And_Sync_Update();
    }
    Check_UART1_Command(); // 处理串口1命令 (HMI)
    Check_UART3_Command(); // 处理串口3命令 (FPGA)
    HAL_Delay(10);         // 简单的延时，防止CPU空转
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}

/**
 * @brief System Clock Configuration
 * @retval None
 */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};

  /** Configure the main internal regulator output voltage
   */
  __HAL_RCC_PWR_CLK_ENABLE();
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
   * in the RCC_OscInitTypeDef structure.
   */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSI;
  RCC_OscInitStruct.HSIState = RCC_HSI_ON;
  RCC_OscInitStruct.HSICalibrationValue = RCC_HSICALIBRATION_DEFAULT;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSI;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 168;
  RCC_OscInitStruct.PLL.PLLP = RCC_PLLP_DIV2;
  RCC_OscInitStruct.PLL.PLLQ = 4;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
   */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_SYSCLK |
                                RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/* USER CODE END 4 */

/**
 * @brief  This function is executed in case of error occurrence.
 * @retval None
 */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

#ifdef USE_FULL_ASSERT
/**
 * @brief  Reports the name of the source file and the source line number
 *         where the assert_param error has occurred.
 * @param  file: pointer to the source file name
 * @param  line: assert_param error line source number
 * @retval None
 */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line
     number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
