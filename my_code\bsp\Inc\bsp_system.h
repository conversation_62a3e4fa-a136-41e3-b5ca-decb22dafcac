#ifndef BSP_SYSTEM_H
#define BSP_SYSTEM_H

/*
****************************************************************************************
*
*                        _oo0oo_
*                       o8888888o
*                       88" . "88
*                       (| -_- |)
*                       0\  =  /0
*                     ___/`---'\___
*                   .' \\|     |// '.
*                  / \\|||  :  |||// \
*                 / _||||| -:- |||||- \
*                |   | \\\  - /// |   |
*                | \_|  ''\---/''  |_/ |
*                \  .-\__  '-'  ___/-. /
*              ___'. .'  /--.--\  `. .'___
*           ."" '<  `.___\_<|>_/___.' >' "".
*          | | :  `- \`.;`\ _ /`;.`/ - ` : | |
*          \  \ `_.   \_ __\ /__ _/   .-` /  /
*      =====`-.____`.___ \_____/___.-`___.-'=====
*                        `=---='
*
*      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
*            佛祖保佑     永无BUG     代码无Bug
****************************************************************************************
*/

#include "main.h"
#include "usart.h"
#include "gpio.h"
#include "stdio.h"
#include "string.h"
#include "stdarg.h"
#include "my_usart.h"
#include "my_usart_pack.h"
#include "AD9959.h"
#include "commond_init.h"
#include "my_hmi.h"
#include "oled.h"

extern u32 Modulated_wave;
extern u8 mdoe_flag;
extern u16 Phase;
extern u32 val;
extern u8 Carrier_indx;
extern int32_t output;
extern u32 pid_vin;

// 测量模块控制函数声明
void Enable_Measurement(void);
void Disable_Measurement(void);

extern volatile uint8_t parameter_changed_flag;
extern volatile uint8_t measurement_enable;
extern volatile uint8_t adc_processing_flag;

// =================================================================
//                      系统参数宏定义
// =================================================================

// 参数限制值
#define FREQ_MIN_HZ 30000000 // 最小频率30MHz
#define FREQ_MAX_HZ 40000000 // 最大频率40MHz
#define FREQ_STEP_HZ 1000000 // 频率步进1MHz
#define AMP_MIN_MV 25        // 最小幅度25mV
#define AMP_MAX_MV 250       // 最大幅度250mV
#define AMP_STEP_MV 25       // 幅度步进25mV
#define PHASE_MIN_DEG 0      // 最小相位0°
#define PHASE_MAX_DEG 180    // 最大相位180°
#define PHASE_STEP_DEG 30    // 相位步进30°

// 时延参数宏定义
#define TIME_DELAY_MIN_NS 50  // 时延最小值50ns
#define TIME_DELAY_MAX_NS 200 // 时延最大值200ns
#define TIME_DELAY_STEP_NS 30 // 时延步进30ns

// 系统状态枚举
typedef enum
{
  SYSTEM_IDLE,              // 空闲状态
  SYSTEM_PARAMETER_CHANGED, // 参数已改变
  SYSTEM_SETTLING,          // 信号稳定中
  SYSTEM_MEASURING          // 测量进行中
} SystemState_t;

// =================================================================
//                      全局变量声明 (外部引用)
// =================================================================
// --- 系统状态 ---
extern SystemState_t system_state;
extern volatile uint8_t parameter_changed_flag; // 参数变更标志
extern volatile uint8_t measurement_enable;     // 测量使能标志
extern volatile uint8_t adc_processing_flag;    // ADC处理标志
extern volatile uint8_t fft_calculate_flag;     // FFT计算标志

// --- CH0 参数 ---
extern uint32_t freq_value_ch0;      // CH0频率(Hz)
extern uint16_t amp_value_mv_ch0;    // CH0幅度(mV)
extern uint16_t phase_value_deg_ch0; // CH0相位(度)
extern uint16_t time_delay_ns_ch0;   // CH0时延(ns)

// --- CH1 参数 ---
extern uint32_t freq_value_ch1;      // CH1频率(Hz)
extern uint16_t amp_value_mv_ch1;    // CH1幅度(mV)
extern uint16_t phase_value_deg_ch1; // CH1相位(度)
extern uint16_t time_delay_ns_ch1;   // CH1时延(ns)

// =================================================================
//                      时延控制函数声明
// =================================================================

// 时延相关函数 (两路都可以调整)
extern float Calculate_Phase_From_Delay(uint32_t freq_hz, uint16_t delay_ns);
extern void Update_CH0_With_Time_Delay(void);
extern void Update_CH1_With_Time_Delay(void);

// =================================================================
//                      双通道同步控制标志位
// =================================================================
extern volatile uint8_t ch0_param_ready_flag; // CH0参数就绪标志
extern volatile uint8_t ch1_param_ready_flag; // CH1参数就绪标志
extern volatile uint8_t sync_update_enable;   // 同步更新使能标志

// =================================================================
//                      系统控制函数声明 (外部接口)
// =================================================================

/**
 * @brief 处理系统状态机
 */
void Process_System_State(void);

/**
 * @brief 设置参数变更标志
 */
void Set_Parameter_Changed(void);

// =================================================================
//                      双通道同步控制函数声明
// =================================================================

/**
 * @brief 启用双通道同步更新模式
 */
void Enable_Sync_Update(void);

/**
 * @brief 禁用双通道同步更新模式
 */
void Disable_Sync_Update(void);

/**
 * @brief 设置CH0参数就绪标志
 */
void Set_CH0_Ready(void);

/**
 * @brief 设置CH1参数就绪标志
 */
void Set_CH1_Ready(void);

/**
 * @brief 检查并执行同步更新
 * @return 1-执行了更新, 0-未执行更新
 */
uint8_t Check_And_Sync_Update(void);

/**
 * @brief 双通道同步参数更新（不立即执行IO_Update）
 * @param ch0_freq CH0频率
 * @param ch0_phase CH0相位
 * @param ch0_amp CH0幅度
 * @param ch1_freq CH1频率
 * @param ch1_phase CH1相位
 * @param ch1_amp CH1幅度
 */
void Update_Dual_Channel_Sync(uint32_t ch0_freq, float ch0_phase, uint16_t ch0_amp,
                              uint32_t ch1_freq, float ch1_phase, uint16_t ch1_amp);

#endif
