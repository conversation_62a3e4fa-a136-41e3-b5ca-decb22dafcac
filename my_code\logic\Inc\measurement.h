#ifndef __MEASUREMENT_H__
#define __MEASUREMENT_H__

#include "main.h" // 包含 main.h 以获取HAL库的类型定义

// =================================================================
//                      函数原型 (对外接口)
// =================================================================

/**
 * @brief 初始化测量模块
 */
void Measurement_Init(void);

/**
 * @brief 启动ADC连续测量 (通过DMA)
 */
void Measurement_Enable(void);

/**
 * @brief 停止ADC测量
 */
void Measurement_Disable(void);

/**
 * @brief 处理直流信号测量（计算、滤波等）
 */
void Measurement_Process_DC(void);

/**
 * @brief 获取当前测量到的幅度值 (mV)
 * @return float 测量结果
 */
void Measurement_Reset_Filter(void); // <--- 在这里添加这一行
float Measurement_Get_Amplitude(void);

// =================================================================
//                      回调函数 (给STM32 HAL用的)
// =================================================================

/**
 * @brief ADC DMA传输完成回调函数
 * @note  这个函数原型需要和HAL库中的弱函数一致，我们在这里声明，在.c中实现
 */
void HAL_ADC_ConvCpltCallback(ADC_HandleTypeDef *hadc);

void Measurement_Enable(void);
void Measurement_Disable(void);
void Measurement_Process_DC(void);

#endif // __MEASUREMENT_H__
