#MicroXplorer Configuration settings - do not modify
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_0
ADC1.ExternalTrigConv=ADC_EXTERNALTRIGCONV_T2_TRGO
ADC1.IPParameters=Rank-1\#ChannelRegularConversion,master,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,NbrOfConversionFlag,ExternalTrigConv
ADC1.NbrOfConversionFlag=1
ADC1.Rank-1\#ChannelRegularConversion=1
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_3CYCLES
ADC1.master=1
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.ADC1.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.ADC1.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.ADC1.0.Instance=DMA2_Stream0
Dma.ADC1.0.MemDataAlignment=DMA_MDATAALIGN_HALFWORD
Dma.ADC1.0.MemInc=DMA_MINC_ENABLE
Dma.ADC1.0.Mode=DMA_CIRCULAR
Dma.ADC1.0.PeriphDataAlignment=DMA_PDATAALIGN_HALFWORD
Dma.ADC1.0.PeriphInc=DMA_PINC_DISABLE
Dma.ADC1.0.Priority=DMA_PRIORITY_LOW
Dma.ADC1.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
Dma.Request0=ADC1
Dma.RequestsNb=1
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407VGT6
Mcu.Family=STM32F4
Mcu.IP0=ADC1
Mcu.IP1=DMA
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IP5=TIM2
Mcu.IP6=USART1
Mcu.IP7=USART2
Mcu.IP8=USART3
Mcu.IPNb=9
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PH0-OSC_IN
Mcu.Pin1=PH1-OSC_OUT
Mcu.Pin10=PA8
Mcu.Pin11=PA9
Mcu.Pin12=PA10
Mcu.Pin13=PA13
Mcu.Pin14=PA14
Mcu.Pin15=PA15
Mcu.Pin16=PC11
Mcu.Pin17=PC12
Mcu.Pin18=PD0
Mcu.Pin19=PD1
Mcu.Pin2=PA0-WKUP
Mcu.Pin20=PD4
Mcu.Pin21=PD5
Mcu.Pin22=PD6
Mcu.Pin23=PD7
Mcu.Pin24=PB4
Mcu.Pin25=PB5
Mcu.Pin26=PB7
Mcu.Pin27=VP_SYS_VS_Systick
Mcu.Pin28=VP_TIM2_VS_ClockSourceINT
Mcu.Pin3=PA2
Mcu.Pin4=PA3
Mcu.Pin5=PB2
Mcu.Pin6=PB10
Mcu.Pin7=PB11
Mcu.Pin8=PC6
Mcu.Pin9=PC7
Mcu.PinsNb=29
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VGTx
MxCube.Version=6.13.0
MxDb.Version=DB.6.0.130
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA2_Stream0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Signal=ADCx_IN0
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA15.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA15.GPIO_Label=AD9959_RST
PA15.GPIO_PuPd=GPIO_PULLUP
PA15.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA15.Locked=true
PA15.Signal=GPIO_Output
PA2.Mode=Asynchronous
PA2.Signal=USART2_TX
PA3.Mode=Asynchronous
PA3.Signal=USART2_RX
PA8.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PA8.GPIO_Label=AD9959_P1
PA8.GPIO_PuPd=GPIO_PULLUP
PA8.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PA8.Locked=true
PA8.Signal=GPIO_Output
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Mode=Asynchronous
PB10.Signal=USART3_TX
PB11.Mode=Asynchronous
PB11.Signal=USART3_RX
PB2.Locked=true
PB2.Signal=GPIO_Output
PB4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB4.GPIO_Label=AD9959_SDIO3
PB4.GPIO_PuPd=GPIO_PULLUP
PB4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB4.Locked=true
PB4.Signal=GPIO_Output
PB5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB5.GPIO_Label=AD9959_SDIO1
PB5.GPIO_PuPd=GPIO_PULLUP
PB5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB5.Locked=true
PB5.Signal=GPIO_Output
PB7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PB7.GPIO_Label=AD9959_SDIO2
PB7.GPIO_PuPd=GPIO_PULLUP
PB7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PB7.Locked=true
PB7.Signal=GPIO_Output
PC11.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PC11.GPIO_Label=OLED_SDA
PC11.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC11.GPIO_PuPd=GPIO_PULLUP
PC11.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC11.Locked=true
PC11.Signal=GPIO_Output
PC12.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultOutputPP
PC12.GPIO_Label=OLED_SCL
PC12.GPIO_ModeDefaultOutputPP=GPIO_MODE_OUTPUT_OD
PC12.GPIO_PuPd=GPIO_PULLUP
PC12.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC12.Locked=true
PC12.Signal=GPIO_Output
PC6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC6.GPIO_Label=AD9959_PDC
PC6.GPIO_PuPd=GPIO_PULLUP
PC6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC6.Locked=true
PC6.Signal=GPIO_Output
PC7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PC7.GPIO_Label=AD9959_P0
PC7.GPIO_PuPd=GPIO_PULLUP
PC7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PC7.Locked=true
PC7.Signal=GPIO_Output
PD0.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD0.GPIO_Label=AD9959_P2
PD0.GPIO_PuPd=GPIO_PULLUP
PD0.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD0.Locked=true
PD0.Signal=GPIO_Output
PD1.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD1.GPIO_Label=AD9959_UP
PD1.GPIO_PuPd=GPIO_PULLUP
PD1.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD1.Locked=true
PD1.Signal=GPIO_Output
PD4.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD4.GPIO_Label=AD9959_P3
PD4.GPIO_PuPd=GPIO_PULLUP
PD4.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD4.Locked=true
PD4.Signal=GPIO_Output
PD5.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD5.GPIO_Label=AD9959_CS
PD5.GPIO_PuPd=GPIO_PULLUP
PD5.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD5.Locked=true
PD5.Signal=GPIO_Output
PD6.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD6.GPIO_Label=AD9959_SDIO0
PD6.GPIO_PuPd=GPIO_PULLUP
PD6.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD6.Locked=true
PD6.Signal=GPIO_Output
PD7.GPIOParameters=GPIO_Speed,GPIO_PuPd,GPIO_Label
PD7.GPIO_Label=AD9959_SCK
PD7.GPIO_PuPd=GPIO_PULLUP
PD7.GPIO_Speed=GPIO_SPEED_FREQ_HIGH
PD7.Locked=true
PD7.Signal=GPIO_Output
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=0
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=AD9959_MCU.ioc
ProjectManager.ProjectName=AD9959_MCU
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_USART1_UART_Init-USART1-false-HAL-true,5-MX_USART2_UART_Init-USART2-false-HAL-true,6-MX_USART3_UART_Init-USART3-false-HAL-true,7-MX_ADC1_Init-ADC1-false-HAL-true,8-MX_TIM2_Init-TIM2-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSE_VALUE,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSE_VALUE=32768
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=8
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.ADCx_IN0.0=ADC1_IN0,IN0
SH.ADCx_IN0.ConfNb=1
TIM2.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_ENABLE
TIM2.IPParameters=Prescaler,Period,AutoReloadPreload,TIM_MasterOutputTrigger
TIM2.Period=99
TIM2.Prescaler=83
TIM2.TIM_MasterOutputTrigger=TIM_TRGO_UPDATE
USART1.BaudRate=9600
USART1.IPParameters=VirtualMode,BaudRate
USART1.VirtualMode=VM_ASYNC
USART2.IPParameters=VirtualMode
USART2.VirtualMode=VM_ASYNC
USART3.BaudRate=9600
USART3.IPParameters=VirtualMode,BaudRate
USART3.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
board=custom
