#ifndef __COMMOND_INIT_H__
#define __COMMOND_INIT_H__

#include "stm32f4xx.h"

//-----------------------------------------------------------------
// 定义一些常用的数据类型短关键字
//-----------------------------------------------------------------
typedef int32_t s32;
typedef int16_t s16;
typedef int8_t s8;

typedef const int32_t sc32;
typedef const int16_t sc16;
typedef const int8_t sc8;

typedef __IO int32_t vs32;
typedef __IO int16_t vs16;
typedef __IO int8_t vs8;

typedef __I int32_t vsc32;
typedef __I int16_t vsc16;
typedef __I int8_t vsc8;

typedef uint32_t u32;
typedef uint16_t u16;
typedef uint8_t u8;

typedef const uint32_t uc32;
typedef const uint16_t uc16;
typedef const uint8_t uc8;

typedef __IO uint32_t vu32;
typedef __IO uint16_t vu16;
typedef __IO uint8_t vu8;

typedef __I uint32_t vuc32;
typedef __I uint16_t vuc16;
typedef __I uint8_t vuc8;

//-----------------------------------------------------------------
// 位带操作, 实现GPIO控制功能
//-----------------------------------------------------------------
#define BITBAND(addr, bitnum) ((addr & 0xF0000000) + 0x2000000 + ((addr & 0xFFFFF) << 5) + (bitnum << 2))
#define MEM_ADDR(addr) (*((volatile unsigned long *)(addr)))
#define BIT_ADDR(addr, bitnum) MEM_ADDR(BITBAND(addr, bitnum))

//-----------------------------------------------------------------
// GPIO寄存器地址定义（输出和输入）
//-----------------------------------------------------------------
#define GPIO_ODR_OFFSET 20 // GPIO 输出寄存器偏移量
#define GPIO_IDR_OFFSET 16 // GPIO 输入寄存器偏移量

#define GPIO_ODR_ADDR(port) ((port##_BASE) + GPIO_ODR_OFFSET)
#define GPIO_IDR_ADDR(port) ((port##_BASE) + GPIO_IDR_OFFSET)

#define GPIOx_OUT(port, n) BIT_ADDR(GPIO_ODR_ADDR(port), n)
#define GPIOx_IN(port, n) BIT_ADDR(GPIO_IDR_ADDR(port), n)

// GPIO快速访问宏
#define PAout(n) GPIOx_OUT(GPIOA, n)
#define PAin(n) GPIOx_IN(GPIOA, n)

#define PBout(n) GPIOx_OUT(GPIOB, n)
#define PBin(n) GPIOx_IN(GPIOB, n)

#define PCout(n) GPIOx_OUT(GPIOC, n)
#define PCin(n) GPIOx_IN(GPIOC, n)

#define PDout(n) GPIOx_OUT(GPIOD, n)
#define PDin(n) GPIOx_IN(GPIOD, n)

#define PEout(n) GPIOx_OUT(GPIOE, n)
#define PEin(n) GPIOx_IN(GPIOE, n)

#define PFout(n) GPIOx_OUT(GPIOF, n)
#define PFin(n) GPIOx_IN(GPIOF, n)

#define PGout(n) GPIOx_OUT(GPIOG, n)
#define PGin(n) GPIOx_IN(GPIOG, n)

#define PHout(n) GPIOx_OUT(GPIOH, n)
#define PHin(n) GPIOx_IN(GPIOH, n)

#define PIout(n) GPIOx_OUT(GPIOI, n)
#define PIin(n) GPIOx_IN(GPIOI, n)

#define PJout(n) GPIOx_OUT(GPIOJ, n)
#define PJin(n) GPIOx_IN(GPIOJ, n)

#define PKout(n) GPIOx_OUT(GPIOK, n)
#define PKin(n) GPIOx_IN(GPIOK, n)

//-----------------------------------------------------------------
// 命令宏定义
//-----------------------------------------------------------------
enum
{
    DA1_FREQ_EN = 1,      // 0000_0000_0000_0001 开启DA1的频率控制
    DA2_FREQ_EN = 2,      // 0000_0000_0000_0010 开启DA2的频率控制
    AD1_FREQ_EN = 4,      // 0000_0000_0000_0100 开启AD1的采样频率控制
    AD2_FREQ_EN = 8,      // 0000_0000_0000_1000 开启AD2的采样频率控制
    AD1_FIFO_WR = 16,     // 0000_0000_0001_0000 开启AD1的FIFO写入
    AD1_FIFO_RD = 32,     // 0000_0000_0010_0000 开启AD1的FIFO读取
    AD2_FIFO_WR = 64,     // 0000_0000_0100_0000 开启AD2的FIFO写入
    AD2_FIFO_RD = 128,    // 0000_0000_1000_0000 开启AD2的FIFO读取
    AD1_FREQ_CLR = 256,   // 0000_0001_0000_0000 清除AD1的采样频率（低电平有效）
    AD1_FREQ_START = 512, // 0000_0010_0000_0000 开启AD1的采样频率计数
    AD2_FREQ_CLR = 1024,  // 0000_0100_0000_0000 清除AD2的采样频率（低电平有效）
    AD2_FREQ_START = 2048 // 0000_1000_0000_0000 开启AD2的采样频率
};

#define reg_addr(addr) ((uint32_t *)(0x64000000 + ((addr) << 1)))
#define CTRL_DATA *(vu16 *)reg_addr(1)
#define DA1_H *(vu16 *)reg_addr(2)
#define DA1_L *(vu16 *)reg_addr(3)
#define DA2_H *(vu16 *)reg_addr(4)
#define DA2_L *(vu16 *)reg_addr(5)
#define AD1_FS_H *(vu16 *)reg_addr(6)
#define AD1_FS_L *(vu16 *)reg_addr(7)
#define AD2_FS_H *(vu16 *)reg_addr(8)
#define AD2_FS_L *(vu16 *)reg_addr(9)
#define AD1_DATA_SHOW *(vu16 *)reg_addr(6)
#define AD1_FULL_FLAG *(vu16 *)reg_addr(7)
#define AD2_DATA_SHOW *(vu16 *)reg_addr(8)
#define AD2_FULL_FLAG *(vu16 *)reg_addr(9)
#define AD1_FREQ_H *(vu16 *)reg_addr(10)
#define AD1_FREQ_L *(vu16 *)reg_addr(11)
#define AD2_FREQ_H *(vu16 *)reg_addr(12)
#define AD2_FREQ_L *(vu16 *)reg_addr(13)
#define BASE1_FREQ_H *(vu16 *)reg_addr(2)
#define BASE1_FREQ_L *(vu16 *)reg_addr(3)
#define BASE2_FREQ_H *(vu16 *)reg_addr(4)
#define BASE2_FREQ_L *(vu16 *)reg_addr(5)
//-----------------------------------------------------------------
// 常量定义
//-----------------------------------------------------------------
#define FIFO_SIZE 1024
#define FIFO_SIZE_N 1024.0f
#define FFT_LENGTH 4096 // FFT长度，默认是1024点FFT

#endif // __COMMOND_INIT_H__
