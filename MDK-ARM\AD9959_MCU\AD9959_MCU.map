Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.Demo_Sync_Phase_Control) refers to bsp_system.o(i.Enable_Sync_Update) for Enable_Sync_Update
    main.o(i.Demo_Sync_Phase_Control) refers to bsp_system.o(i.Update_Dual_Channel_Sync) for Update_Dual_Channel_Sync
    main.o(i.Demo_Sync_Phase_Control) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.Demo_Sync_Phase_Control) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.Demo_Sync_Phase_Control) refers to usart.o(.bss) for huart1
    main.o(i.Set_Precise_Dual_Channel) refers to bsp_system.o(i.Enable_Sync_Update) for Enable_Sync_Update
    main.o(i.Set_Precise_Dual_Channel) refers to bsp_system.o(i.Update_Dual_Channel_Sync) for Update_Dual_Channel_Sync
    main.o(i.Set_Precise_Dual_Channel) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(i.Set_Precise_Dual_Channel) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.Set_Precise_Dual_Channel) refers to main.o(.conststring) for .conststring
    main.o(i.Set_Precise_Dual_Channel) refers to usart.o(.bss) for huart1
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.System_Init_Sync_Control) refers to bsp_system.o(i.Disable_Sync_Update) for Disable_Sync_Update
    main.o(i.System_Init_Sync_Control) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.System_Init_Sync_Control) refers to usart.o(.bss) for huart1
    main.o(i.System_Init_Time_Delay) refers to ad9959.o(i.Update_CH1_With_Time_Delay) for Update_CH1_With_Time_Delay
    main.o(i.System_Init_Time_Delay) refers to bsp_system.o(.data) for time_delay_ns_ch1
    main.o(i.Test_Sync_Control) refers to my_usart.o(i.my_printf) for my_printf
    main.o(i.Test_Sync_Control) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    main.o(i.Test_Sync_Control) refers to main.o(i.Demo_Sync_Phase_Control) for Demo_Sync_Phase_Control
    main.o(i.Test_Sync_Control) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.Test_Sync_Control) refers to bsp_system.o(i.Disable_Sync_Update) for Disable_Sync_Update
    main.o(i.Test_Sync_Control) refers to usart.o(.bss) for huart1
    main.o(i.Test_Sync_Control) refers to main.o(.constdata) for .constdata
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART2_UART_Init) for MX_USART2_UART_Init
    main.o(i.main) refers to usart.o(i.MX_USART3_UART_Init) for MX_USART3_UART_Init
    main.o(i.main) refers to adc.o(i.MX_ADC1_Init) for MX_ADC1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    main.o(i.main) refers to ad9959.o(i.AD9959_Init) for AD9959_Init
    main.o(i.main) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    main.o(i.main) refers to main.o(i.System_Init_Time_Delay) for System_Init_Time_Delay
    main.o(i.main) refers to main.o(i.System_Init_Sync_Control) for System_Init_Sync_Control
    main.o(i.main) refers to my_hmi.o(i.HMI_Send_Float) for HMI_Send_Float
    main.o(i.main) refers to my_hmi.o(i.HMI_Send_Int) for HMI_Send_Int
    main.o(i.main) refers to my_hmi.o(i.HMI_Debug_Print) for HMI_Debug_Print
    main.o(i.main) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start) for HAL_TIM_Base_Start
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to measurement.o(i.Measurement_Enable) for Measurement_Enable
    main.o(i.main) refers to my_usart.o(i.Send_FPGA_Command) for Send_FPGA_Command
    main.o(i.main) refers to bsp_system.o(i.Process_System_State) for Process_System_State
    main.o(i.main) refers to bsp_system.o(i.Check_And_Sync_Update) for Check_And_Sync_Update
    main.o(i.main) refers to my_usart.o(i.Check_UART1_Command) for Check_UART1_Command
    main.o(i.main) refers to my_usart.o(i.Check_UART3_Command) for Check_UART3_Command
    main.o(i.main) refers to my_usart.o(.data) for rxTemp1
    main.o(i.main) refers to usart.o(.bss) for huart1
    main.o(i.main) refers to bsp_system.o(.data) for freq_value_ch0
    main.o(i.main) refers to bsp_system.o(.data) for time_delay_ns_ch0
    main.o(i.main) refers to bsp_system.o(.data) for time_delay_ns_ch1
    main.o(i.main) refers to tim.o(.bss) for htim2
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    adc.o(i.HAL_ADC_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    adc.o(i.HAL_ADC_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    adc.o(i.HAL_ADC_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    adc.o(i.HAL_ADC_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.HAL_ADC_MspInit) refers to adc.o(.bss) for .bss
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Init) for HAL_ADC_Init
    adc.o(i.MX_ADC1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    adc.o(i.MX_ADC1_Init) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) for HAL_ADC_ConfigChannel
    adc.o(i.MX_ADC1_Init) refers to adc.o(.bss) for .bss
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for .bss
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART2_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART2_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART2_UART_Init) refers to usart.o(.bss) for .bss
    usart.o(i.MX_USART3_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART3_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART3_UART_Init) refers to usart.o(.bss) for .bss
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.bss) for hdma_adc1
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) refers to measurement.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit) refers to adc.o(i.HAL_ADC_MspDeInit) for HAL_ADC_MspDeInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to measurement.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback) for HAL_ADCEx_InjectedConvCpltCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback) for HAL_ADC_LevelOutOfWindowCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to adc.o(i.HAL_ADC_MspInit) for HAL_ADC_MspInit
    stm32f4xx_hal_adc.o(i.HAL_ADC_Init) refers to stm32f4xx_hal_adc.o(i.ADC_Init) for ADC_Init
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt) for ADC_DMAConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt) for ADC_DMAHalfConvCplt
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) refers to stm32f4xx_hal_adc.o(i.ADC_DMAError) for ADC_DMAError
    stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) refers to measurement.o(i.HAL_ADC_ConvCpltCallback) for HAL_ADC_ConvCpltCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback) for HAL_ADC_ErrorCallback
    stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback) for HAL_ADC_ConvHalfCpltCallback
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt) for ADC_MultiModeDMAConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt) for ADC_MultiModeDMAHalfConvCplt
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA) refers to stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError) for ADC_MultiModeDMAError
    stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for .bss
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for .constdata
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for .data
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to my_usart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for .constdata
    bsp_system.o(i.Check_And_Sync_Update) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    bsp_system.o(i.Check_And_Sync_Update) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    bsp_system.o(i.Check_And_Sync_Update) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Disable_Sync_Update) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Enable_Sync_Update) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Process_System_State) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_system.o(i.Process_System_State) refers to measurement.o(i.Measurement_Process_DC) for Measurement_Process_DC
    bsp_system.o(i.Process_System_State) refers to measurement.o(i.Measurement_Reset_Filter) for Measurement_Reset_Filter
    bsp_system.o(i.Process_System_State) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Set_CH0_Ready) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Set_CH1_Ready) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Set_Parameter_Changed) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bsp_system.o(i.Set_Parameter_Changed) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Update_Amplitude_CH0) refers to measurement.o(i.Measurement_Disable) for Measurement_Disable
    bsp_system.o(i.Update_Amplitude_CH0) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    bsp_system.o(i.Update_Amplitude_CH0) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    bsp_system.o(i.Update_Amplitude_CH0) refers to measurement.o(i.Measurement_Enable) for Measurement_Enable
    bsp_system.o(i.Update_Amplitude_CH0) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Update_Amplitude_CH1) refers to measurement.o(i.Measurement_Disable) for Measurement_Disable
    bsp_system.o(i.Update_Amplitude_CH1) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    bsp_system.o(i.Update_Amplitude_CH1) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    bsp_system.o(i.Update_Amplitude_CH1) refers to measurement.o(i.Measurement_Enable) for Measurement_Enable
    bsp_system.o(i.Update_Amplitude_CH1) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to measurement.o(i.Measurement_Disable) for Measurement_Disable
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to ad9959.o(i.AD9959_Set_Fre_NoUpdate) for AD9959_Set_Fre_NoUpdate
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to ad9959.o(i.AD9959_Set_Pha_NoUpdate) for AD9959_Set_Pha_NoUpdate
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to ad9959.o(i.Convert_mV_to_AD9959_Amp) for Convert_mV_to_AD9959_Amp
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to ad9959.o(i.AD9959_Set_Amp_NoUpdate) for AD9959_Set_Amp_NoUpdate
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to bsp_system.o(i.Set_CH0_Ready) for Set_CH0_Ready
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to bsp_system.o(i.Set_CH1_Ready) for Set_CH1_Ready
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to bsp_system.o(i.Check_And_Sync_Update) for Check_And_Sync_Update
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to measurement.o(i.Measurement_Enable) for Measurement_Enable
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    bsp_system.o(i.Update_Dual_Channel_Sync) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Update_Frequency_CH0) refers to measurement.o(i.Measurement_Disable) for Measurement_Disable
    bsp_system.o(i.Update_Frequency_CH0) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    bsp_system.o(i.Update_Frequency_CH0) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    bsp_system.o(i.Update_Frequency_CH0) refers to measurement.o(i.Measurement_Enable) for Measurement_Enable
    bsp_system.o(i.Update_Frequency_CH0) refers to bsp_system.o(.data) for .data
    bsp_system.o(i.Update_Frequency_CH1) refers to measurement.o(i.Measurement_Disable) for Measurement_Disable
    bsp_system.o(i.Update_Frequency_CH1) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    bsp_system.o(i.Update_Frequency_CH1) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    bsp_system.o(i.Update_Frequency_CH1) refers to measurement.o(i.Measurement_Enable) for Measurement_Enable
    bsp_system.o(i.Update_Frequency_CH1) refers to bsp_system.o(.data) for .data
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(i.delay_xms) for delay_xms
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    delay.o(i.delay_xms) refers to delay.o(.data) for .data
    measurement.o(i.HAL_ADC_ConvCpltCallback) refers to bsp_system.o(.data) for measurement_enable
    measurement.o(i.HAL_ADC_ConvCpltCallback) refers to measurement.o(.data) for .data
    measurement.o(i.HAL_ADC_ConvCpltCallback) refers to bsp_system.o(.data) for adc_processing_flag
    measurement.o(i.Measurement_Disable) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    measurement.o(i.Measurement_Disable) refers to bsp_system.o(.data) for measurement_enable
    measurement.o(i.Measurement_Disable) refers to adc.o(.bss) for hadc1
    measurement.o(i.Measurement_Enable) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    measurement.o(i.Measurement_Enable) refers to bsp_system.o(.data) for measurement_enable
    measurement.o(i.Measurement_Enable) refers to bsp_system.o(.data) for adc_processing_flag
    measurement.o(i.Measurement_Enable) refers to measurement.o(.bss) for .bss
    measurement.o(i.Measurement_Enable) refers to adc.o(.bss) for hadc1
    measurement.o(i.Measurement_Get_Amplitude) refers to measurement.o(.data) for .data
    measurement.o(i.Measurement_Init) refers to measurement.o(.data) for .data
    measurement.o(i.Measurement_Process_DC) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA) for HAL_ADC_Stop_DMA
    measurement.o(i.Measurement_Process_DC) refers to my_hmi.o(i.HMI_Send_Int) for HMI_Send_Int
    measurement.o(i.Measurement_Process_DC) refers to stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA) for HAL_ADC_Start_DMA
    measurement.o(i.Measurement_Process_DC) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    measurement.o(i.Measurement_Process_DC) refers to bsp_system.o(.data) for adc_processing_flag
    measurement.o(i.Measurement_Process_DC) refers to adc.o(.bss) for hadc1
    measurement.o(i.Measurement_Process_DC) refers to measurement.o(.bss) for .bss
    measurement.o(i.Measurement_Process_DC) refers to measurement.o(.data) for .data
    measurement.o(i.Measurement_Process_DC) refers to bsp_system.o(.data) for measurement_enable
    measurement.o(i.Measurement_Process_DC) refers to bsp_system.o(.data) for last_measurement_time
    measurement.o(i.Measurement_Reset_Filter) refers to measurement.o(.data) for .data
    ad9959.o(i.AD9959_CS_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_CS_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Ch) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_IO_UpDate) refers to ad9959.o(i.AD9959_UP_L) for AD9959_UP_L
    ad9959.o(i.AD9959_IO_UpDate) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_IO_UpDate) refers to ad9959.o(i.AD9959_UP_H) for AD9959_UP_H
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_PDC_L) for AD9959_PDC_L
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_Start) for AD9959_Start
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_Reset) for AD9959_Reset
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_P0_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P0_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P1_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P1_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P2_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P2_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P3_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_P3_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_PDC_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_PDC_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_RST_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_RST_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Reset) refers to ad9959.o(i.AD9959_RST_L) for AD9959_RST_L
    ad9959.o(i.AD9959_Reset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_Reset) refers to ad9959.o(i.AD9959_RST_H) for AD9959_RST_H
    ad9959.o(i.AD9959_SCK_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SCK_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO0_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO0_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO1_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO1_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO2_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO2_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO3_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_SDIO3_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_Set_Amp) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Amp) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Set_Amp_NoUpdate) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Fre) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9959.o(i.AD9959_Set_Fre) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9959.o(i.AD9959_Set_Fre) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9959.o(i.AD9959_Set_Fre) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Fre) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Set_Fre_NoUpdate) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9959.o(i.AD9959_Set_Fre_NoUpdate) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9959.o(i.AD9959_Set_Fre_NoUpdate) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9959.o(i.AD9959_Set_Fre_NoUpdate) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Pha) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Set_Pha) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_Set_Pha_NoUpdate) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Fre) for AD9959_Set_Fre
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Pha) for AD9959_Set_Pha
    ad9959.o(i.AD9959_Single_Output) refers to ad9959.o(i.AD9959_Set_Amp) for AD9959_Set_Amp
    ad9959.o(i.AD9959_Single_Output_NoUpdate) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959_Single_Output_NoUpdate) refers to ad9959.o(i.AD9959_Set_Fre_NoUpdate) for AD9959_Set_Fre_NoUpdate
    ad9959.o(i.AD9959_Single_Output_NoUpdate) refers to ad9959.o(i.AD9959_Set_Pha_NoUpdate) for AD9959_Set_Pha_NoUpdate
    ad9959.o(i.AD9959_Single_Output_NoUpdate) refers to ad9959.o(i.AD9959_Set_Amp_NoUpdate) for AD9959_Set_Amp_NoUpdate
    ad9959.o(i.AD9959_Start) refers to ad9959.o(i.AD9959_CS_H) for AD9959_CS_H
    ad9959.o(i.AD9959_Start) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_Start) refers to ad9959.o(i.AD9959_CS_L) for AD9959_CS_L
    ad9959.o(i.AD9959_Sweep_Phase) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9959.o(i.AD9959_Sweep_Phase) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9959.o(i.AD9959_Sweep_Phase) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9959.o(i.AD9959_Sweep_Phase) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959_Sweep_Phase) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959_UP_H) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_UP_L) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SDIO0_H) for AD9959_SDIO0_H
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SDIO0_L) for AD9959_SDIO0_L
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SCK_H) for AD9959_SCK_H
    ad9959.o(i.AD9959_WByte) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959_WByte) refers to ad9959.o(i.AD9959_SCK_L) for AD9959_SCK_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_SDIO3_L) for AD9959_SDIO3_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_CS_L) for AD9959_CS_L
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_WByte) for AD9959_WByte
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_CS_H) for AD9959_CS_H
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(i.AD9959_SDIO3_H) for AD9959_SDIO3_H
    ad9959.o(i.AD9959_WRrg) refers to ad9959.o(.data) for .data
    ad9959.o(i.AD9959__Sweep_Amp) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959__Sweep_Amp) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959__Sweep_Fre) refers to dflt_clz.o(x$fpl$dfltu) for __aeabi_ui2d
    ad9959.o(i.AD9959__Sweep_Fre) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    ad9959.o(i.AD9959__Sweep_Fre) refers to dfixu.o(x$fpl$dfixu) for __aeabi_d2uiz
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_Ch) for AD9959_Ch
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_WRrg) for AD9959_WRrg
    ad9959.o(i.AD9959__Sweep_Fre) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P0_L) for AD9959_P0_L
    ad9959.o(i.AD9959__Sweep_Trigger) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    ad9959.o(i.AD9959__Sweep_Trigger) refers to ad9959.o(i.AD9959_P0_H) for AD9959_P0_H
    ad9959.o(i.AD9959_proc) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    ad9959.o(i.AD9959_proc) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.AD9959_proc) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin) for HAL_GPIO_TogglePin
    ad9959.o(i.AD9959_proc) refers to ad9959.o(.data) for .data
    ad9959.o(i.Update_CH0_Frequency_With_Delay_Linkage) refers to ad9959.o(i.Update_CH0_With_Time_Delay) for Update_CH0_With_Time_Delay
    ad9959.o(i.Update_CH0_Frequency_With_Delay_Linkage) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    ad9959.o(i.Update_CH0_Frequency_With_Delay_Linkage) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    ad9959.o(i.Update_CH0_Frequency_With_Delay_Linkage) refers to bsp_system.o(.data) for freq_value_ch0
    ad9959.o(i.Update_CH0_Frequency_With_Delay_Linkage) refers to bsp_system.o(.data) for time_delay_ns_ch0
    ad9959.o(i.Update_CH0_With_Time_Delay) refers to ad9959.o(i.Calculate_Phase_From_Delay) for Calculate_Phase_From_Delay
    ad9959.o(i.Update_CH0_With_Time_Delay) refers to ad9959.o(i.Convert_mV_to_AD9959_Amp) for Convert_mV_to_AD9959_Amp
    ad9959.o(i.Update_CH0_With_Time_Delay) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    ad9959.o(i.Update_CH0_With_Time_Delay) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.Update_CH0_With_Time_Delay) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    ad9959.o(i.Update_CH0_With_Time_Delay) refers to bsp_system.o(.data) for time_delay_ns_ch0
    ad9959.o(i.Update_CH0_With_Time_Delay) refers to bsp_system.o(.data) for freq_value_ch0
    ad9959.o(i.Update_CH1_Frequency_With_Delay_Linkage) refers to ad9959.o(i.Update_CH1_With_Time_Delay) for Update_CH1_With_Time_Delay
    ad9959.o(i.Update_CH1_Frequency_With_Delay_Linkage) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    ad9959.o(i.Update_CH1_Frequency_With_Delay_Linkage) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    ad9959.o(i.Update_CH1_Frequency_With_Delay_Linkage) refers to bsp_system.o(.data) for freq_value_ch1
    ad9959.o(i.Update_CH1_Frequency_With_Delay_Linkage) refers to bsp_system.o(.data) for time_delay_ns_ch1
    ad9959.o(i.Update_CH1_With_Time_Delay) refers to ad9959.o(i.Calculate_Phase_From_Delay) for Calculate_Phase_From_Delay
    ad9959.o(i.Update_CH1_With_Time_Delay) refers to ad9959.o(i.Convert_mV_to_AD9959_Amp) for Convert_mV_to_AD9959_Amp
    ad9959.o(i.Update_CH1_With_Time_Delay) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    ad9959.o(i.Update_CH1_With_Time_Delay) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.Update_CH1_With_Time_Delay) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    ad9959.o(i.Update_CH1_With_Time_Delay) refers to bsp_system.o(.data) for time_delay_ns_ch1
    ad9959.o(i.Update_CH1_With_Time_Delay) refers to bsp_system.o(.data) for freq_value_ch1
    ad9959.o(i.Update_Channel_Output) refers to ad9959.o(i.Convert_mV_to_AD9959_Amp) for Convert_mV_to_AD9959_Amp
    ad9959.o(i.Update_Channel_Output) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    ad9959.o(i.Update_Channel_Output) refers to ad9959.o(i.AD9959_IO_UpDate) for AD9959_IO_UpDate
    ad9959.o(i.Update_Channel_Output) refers to bsp_system.o(.data) for amp_value_mv_ch0
    ad9959.o(i.Update_Channel_Output_Sync) refers to ad9959.o(i.Convert_mV_to_AD9959_Amp) for Convert_mV_to_AD9959_Amp
    ad9959.o(i.Update_Channel_Output_Sync) refers to ad9959.o(i.AD9959_Single_Output_NoUpdate) for AD9959_Single_Output_NoUpdate
    ad9959.o(i.Update_Channel_Output_Sync) refers to bsp_system.o(i.Set_CH0_Ready) for Set_CH0_Ready
    ad9959.o(i.Update_Channel_Output_Sync) refers to bsp_system.o(i.Set_CH1_Ready) for Set_CH1_Ready
    ad9959.o(i.Update_Channel_Output_Sync) refers to ad9959.o(i.AD9959_Single_Output) for AD9959_Single_Output
    ad9959.o(i.Update_Channel_Output_Sync) refers to bsp_system.o(i.Check_And_Sync_Update) for Check_And_Sync_Update
    ad9959.o(i.Update_Channel_Output_Sync) refers to bsp_system.o(.data) for sync_update_enable
    oled.o(i.I2C_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.I2C_Start) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.I2C_Stop) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.I2C_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.I2C_WaitAck) refers to oled.o(i.IIC_delay) for IIC_delay
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_Clear) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisPlay_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawCircle) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawLine) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_DrawPoint) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Refresh) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(i.I2C_Start) for I2C_Start
    oled.o(i.OLED_Refresh) refers to oled.o(i.Send_Byte) for Send_Byte
    oled.o(i.OLED_Refresh) refers to oled.o(i.I2C_WaitAck) for I2C_WaitAck
    oled.o(i.OLED_Refresh) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.OLED_Refresh) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_ShowChinese) for OLED_ShowChinese
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(i.OLED_Refresh) for OLED_Refresh
    oled.o(i.OLED_ScrollDisplay) refers to oled.o(.bss) for .bss
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowChinese) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPicture) refers to oled.o(i.OLED_DrawPoint) for OLED_DrawPoint
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Start) for I2C_Start
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.Send_Byte) for Send_Byte
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_WaitAck) for I2C_WaitAck
    oled.o(i.OLED_WR_Byte) refers to oled.o(i.I2C_Stop) for I2C_Stop
    oled.o(i.Send_Byte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.Send_Byte) refers to oled.o(i.IIC_delay) for IIC_delay
    my_hmi.o(i.HMI_Debug_Print) refers to vsnprintf.o(.text) for vsnprintf
    my_hmi.o(i.HMI_Debug_Print) refers to my_hmi.o(i.HMI_Send_String) for HMI_Send_String
    my_hmi.o(i.HMI_Send_Float) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    my_hmi.o(i.HMI_Send_Float) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    my_hmi.o(i.HMI_Send_Float) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    my_hmi.o(i.HMI_Send_Float) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    my_hmi.o(i.HMI_Send_Float) refers to dfix.o(x$fpl$dfix) for __aeabi_d2iz
    my_hmi.o(i.HMI_Send_Float) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_Float) refers to usart.o(.bss) for huart1
    my_hmi.o(i.HMI_Send_Int) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_Int) refers to usart.o(.bss) for huart1
    my_hmi.o(i.HMI_Send_String) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Send_String) refers to usart.o(.bss) for huart1
    my_hmi.o(i.HMI_Wave_Clear) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Wave_Clear) refers to usart.o(.bss) for huart1
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    my_hmi.o(i.HMI_Write_Wave_Fast) refers to usart.o(.bss) for huart1
    my_hmi.o(i.HMI_Write_Wave_Low) refers to my_usart.o(i.my_printf) for my_printf
    my_hmi.o(i.HMI_Write_Wave_Low) refers to usart.o(.bss) for huart1
    my_usart.o(i.Check_UART1_Command) refers to my_usart.o(i.Parse_Command_UART1) for Parse_Command_UART1
    my_usart.o(i.Check_UART1_Command) refers to my_usart.o(.data) for .data
    my_usart.o(i.Check_UART1_Command) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.Check_UART3_Command) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.Check_UART3_Command) refers to my_hmi.o(i.HMI_Send_String) for HMI_Send_String
    my_usart.o(i.Check_UART3_Command) refers to my_usart.o(.data) for .data
    my_usart.o(i.Check_UART3_Command) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.Check_UART3_Command) refers to usart.o(.bss) for huart1
    my_usart.o(i.Display_Relative_Delay_Info) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.Display_Relative_Delay_Info) refers to bsp_system.o(.data) for time_delay_ns_ch1
    my_usart.o(i.Display_Relative_Delay_Info) refers to bsp_system.o(.data) for time_delay_ns_ch0
    my_usart.o(i.Display_Relative_Delay_Info) refers to usart.o(.bss) for huart1
    my_usart.o(i.Get_CH0_Modulation_Depth) refers to my_usart.o(.data) for .data
    my_usart.o(i.Get_CH0_Time_Delay) refers to bsp_system.o(.data) for time_delay_ns_ch0
    my_usart.o(i.Get_CH1_Modulation_Depth) refers to my_usart.o(.data) for .data
    my_usart.o(i.Get_CH1_Time_Delay) refers to bsp_system.o(.data) for time_delay_ns_ch1
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.data) for .data
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to my_usart.o(.bss) for .bss
    my_usart.o(i.HAL_UART_RxCpltCallback) refers to usart.o(.bss) for huart1
    my_usart.o(i.Parse_Command_UART1) refers to strcmpv7m.o(.text) for strcmp
    my_usart.o(i.Parse_Command_UART1) refers to ad9959.o(i.Update_Channel_Output) for Update_Channel_Output
    my_usart.o(i.Parse_Command_UART1) refers to bsp_system.o(i.Set_Parameter_Changed) for Set_Parameter_Changed
    my_usart.o(i.Parse_Command_UART1) refers to my_hmi.o(i.HMI_Send_Float) for HMI_Send_Float
    my_usart.o(i.Parse_Command_UART1) refers to my_hmi.o(i.HMI_Send_Int) for HMI_Send_Int
    my_usart.o(i.Parse_Command_UART1) refers to my_usart.o(i.Send_FPGA_Command) for Send_FPGA_Command
    my_usart.o(i.Parse_Command_UART1) refers to bsp_system.o(.data) for freq_value_ch0
    my_usart.o(i.Parse_Command_UART1) refers to bsp_system.o(.data) for time_delay_ns_ch0
    my_usart.o(i.Parse_Command_UART1) refers to bsp_system.o(.data) for time_delay_ns_ch1
    my_usart.o(i.Parse_Command_UART1) refers to my_usart.o(.data) for .data
    my_usart.o(i.Parse_Command_UART1) refers to ad9959.o(i.Update_CH0_With_Time_Delay) for Update_CH0_With_Time_Delay
    my_usart.o(i.Parse_Command_UART1) refers to my_hmi.o(i.HMI_Send_String) for HMI_Send_String
    my_usart.o(i.Parse_Command_UART1) refers to ad9959.o(i.Calculate_Phase_From_Delay) for Calculate_Phase_From_Delay
    my_usart.o(i.Parse_Command_UART1) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    my_usart.o(i.Parse_Command_UART1) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.Parse_Command_UART1) refers to ad9959.o(i.Update_CH1_With_Time_Delay) for Update_CH1_With_Time_Delay
    my_usart.o(i.Parse_Command_UART1) refers to my_usart.o(i.Send_Peak_Value_To_FPGA) for Send_Peak_Value_To_FPGA
    my_usart.o(i.Parse_Command_UART1) refers to usart.o(.bss) for huart1
    my_usart.o(i.Parse_Command_UART1) refers to my_usart.o(i.Display_Relative_Delay_Info) for Display_Relative_Delay_Info
    my_usart.o(i.Parse_Command_UART1) refers to my_hmi.o(i.HMI_Debug_Print) for HMI_Debug_Print
    my_usart.o(i.Parse_Command_UART1) refers to my_usart.o(.conststring) for .conststring
    my_usart.o(i.Send_FPGA_Command) refers to strlen.o(.text) for strlen
    my_usart.o(i.Send_FPGA_Command) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart.o(i.Send_FPGA_Command) refers to usart.o(.bss) for huart3
    my_usart.o(i.Send_Peak_Value_To_FPGA) refers to my_usart.o(i.my_printf) for my_printf
    my_usart.o(i.Send_Peak_Value_To_FPGA) refers to bsp_system.o(.data) for amp_value_mv_ch1
    my_usart.o(i.Send_Peak_Value_To_FPGA) refers to usart.o(.bss) for huart3
    my_usart.o(i.USART1_Init_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.USART1_Init_IT) refers to my_usart.o(.data) for .data
    my_usart.o(i.USART1_Init_IT) refers to usart.o(.bss) for huart1
    my_usart.o(i.USART3_Init_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) for HAL_UART_Receive_IT
    my_usart.o(i.USART3_Init_IT) refers to my_usart.o(.data) for .data
    my_usart.o(i.USART3_Init_IT) refers to usart.o(.bss) for huart3
    my_usart.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    my_usart.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.ParseDataToVariables) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.ParseFrame) refers to my_usart_pack.o(i.ParseDataToVariables) for ParseDataToVariables
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.PrepareFrame) refers to my_usart_pack.o(.data) for .data
    my_usart_pack.o(i.SendFrame) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    my_usart_pack.o(i.SendFrame) refers to usart.o(.bss) for huart2
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.bss) for .bss
    my_usart_pack.o(i.SetParseTemplate) refers to my_usart_pack.o(.data) for .data
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    dfix.o(x$fpl$dfix) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfix) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfix.o(x$fpl$dfixr) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfix.o(x$fpl$dfixr) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixu) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dfixu.o(x$fpl$dfixur) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dfixu.o(x$fpl$dfixur) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    pow.o(i.__hardfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow.o(i.__hardfp_pow) refers to _rserrno.o(.text) for __set_errno
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_divzero) for __mathlib_dbl_divzero
    pow.o(i.__hardfp_pow) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_overflow) for __mathlib_dbl_overflow
    pow.o(i.__hardfp_pow) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow.o(i.__hardfp_pow) refers to sqrt.o(i.sqrt) for sqrt
    pow.o(i.__hardfp_pow) refers to fabs.o(i.fabs) for fabs
    pow.o(i.__hardfp_pow) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    pow.o(i.__hardfp_pow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow.o(i.__hardfp_pow) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow.o(i.__hardfp_pow) refers to qnan.o(.constdata) for __mathlib_zero
    pow.o(i.__hardfp_pow) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow.o(i.__hardfp_pow) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow.o(i.__hardfp_pow) refers to pow.o(.constdata) for .constdata
    pow.o(i.__hardfp_pow) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow.o(i.__hardfp_pow) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow.o(i.__hardfp_pow) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    pow.o(i.__softfp_pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.__softfp_pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(i.pow) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow.o(i.pow) refers to pow.o(i.__hardfp_pow) for __hardfp_pow
    pow.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____hardfp_pow$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    pow_x.o(i.____hardfp_pow$lsc) refers to _rserrno.o(.text) for __set_errno
    pow_x.o(i.____hardfp_pow$lsc) refers to basic.o(x$fpl$basic) for __aeabi_dneg
    pow_x.o(i.____hardfp_pow$lsc) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    pow_x.o(i.____hardfp_pow$lsc) refers to sqrt.o(i.sqrt) for sqrt
    pow_x.o(i.____hardfp_pow$lsc) refers to fabs.o(i.fabs) for fabs
    pow_x.o(i.____hardfp_pow$lsc) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    pow_x.o(i.____hardfp_pow$lsc) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dsub) for __aeabi_dsub
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$drsb) for __aeabi_drsub
    pow_x.o(i.____hardfp_pow$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    pow_x.o(i.____hardfp_pow$lsc) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    pow_x.o(i.____hardfp_pow$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    pow_x.o(i.____hardfp_pow$lsc) refers to pow_x.o(.constdata) for .constdata
    pow_x.o(i.____hardfp_pow$lsc) refers to drleqf.o(x$fpl$drleqf) for __aeabi_cdrcmple
    pow_x.o(i.____hardfp_pow$lsc) refers to scalbn.o(x$fpl$scalbn) for __ARM_scalbn
    pow_x.o(i.____softfp_pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.____softfp_pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(i.__pow$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    pow_x.o(i.__pow$lsc) refers to pow_x.o(i.____hardfp_pow$lsc) for ____hardfp_pow$lsc
    pow_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    basic.o(x$fpl$basic) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dadd) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dadd) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    daddsub_clz.o(x$fpl$dadd) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    daddsub_clz.o(x$fpl$drsb) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$drsb) refers to daddsub_clz.o(x$fpl$dsub) for _dsub1
    daddsub_clz.o(x$fpl$dsub) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    daddsub_clz.o(x$fpl$dsub) refers to daddsub_clz.o(x$fpl$dadd) for _dadd1
    daddsub_clz.o(x$fpl$dsub) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    drleqf.o(x$fpl$drleqf) refers to dleqf.o(x$fpl$dleqf) for __fpl_dcmple_InfNaN
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    scalbn.o(x$fpl$scalbn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    scalbn.o(x$fpl$scalbn) refers to dcheck1.o(x$fpl$dcheck1) for __fpl_dcheck_NaN1
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to daddsub_clz.o(x$fpl$dadd) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt.o(i.sqrt) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to dleqf.o(x$fpl$dleqf) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt_umaal.o(x$fpl$dsqrt) for _dsqrt
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    dcheck1.o(x$fpl$dcheck1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dcheck1.o(x$fpl$dcheck1) refers to retnan.o(x$fpl$retnan) for __fpl_return_NaN
    dleqf.o(x$fpl$dleqf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dleqf.o(x$fpl$dleqf) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dleqf.o(x$fpl$dleqf) refers to dcmpi.o(x$fpl$dcmpinf) for __fpl_dcmp_Inf
    dsqrt_umaal.o(x$fpl$dsqrt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dsqrt_umaal.o(x$fpl$dsqrt) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    dcmpi.o(x$fpl$dcmpinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    retnan.o(x$fpl$retnan) refers to trapv.o(x$fpl$trapveneer) for __fpl_cmpreturn
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    trapv.o(x$fpl$trapveneer) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000007) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_user_alloc_1
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(i.Demo_Sync_Phase_Control), (124 bytes).
    Removing main.o(i.Set_Precise_Dual_Channel), (180 bytes).
    Removing main.o(i.Test_Sync_Control), (168 bytes).
    Removing main.o(.constdata), (24 bytes).
    Removing main.o(.conststring), (71 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.HAL_ADC_MspDeInit), (56 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (24 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (116 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_AnalogWDGConfig), (112 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_ConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_DeInit), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetError), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetState), (4 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_GetValue), (6 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_IRQHandler), (302 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_LevelOutOfWindowCallback), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForConversion), (170 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_PollForEvent), (118 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start), (252 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop), (60 bytes).
    Removing stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_IT), (72 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_adc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAConvCplt), (90 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAError), (18 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.ADC_MultiModeDMAHalfConvCplt), (6 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConfigChannel), (444 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedConvCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedGetValue), (58 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedPollForConversion), (144 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart), (220 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStart_IT), (228 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop), (86 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_InjectedStop_IT), (94 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeConfigChannel), (84 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeGetValue), (12 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStart_DMA), (272 bytes).
    Removing stm32f4xx_hal_adc_ex.o(i.HAL_ADCEx_MultiModeStop_DMA), (96 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (64 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (180 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (140 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (104 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (96 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (316 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (308 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (32 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (104 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (80 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (200 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (24 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (124 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (92 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (80 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (84 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (152 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (64 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (204 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (24 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (34 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (30 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (14 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (100 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (2432 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (98 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (288 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (82 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (70 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (60 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (88 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (84 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (82 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (72 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (36 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (14 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (108 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (144 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (12 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (168 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (192 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (48 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (216 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (16 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (332 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (18 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (106 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start), (142 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (428 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (182 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (172 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (4 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (34 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (292 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (228 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (460 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (146 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler), (304 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (82 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (230 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (132 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (92 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init), (90 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start), (200 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (448 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (124 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (204 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (188 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (86 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (110 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (84 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig), (80 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (128 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (54 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (144 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (112 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (76 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (208 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (224 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (180 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (58 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (68 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (100 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (98 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (120 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (192 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (392 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (232 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (104 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (168 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (170 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (28 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (16 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (26 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (74 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (130 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (60 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (240 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (78 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (210 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (148 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (152 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (98 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (104 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (244 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (120 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (176 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (132 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAError), (74 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt), (134 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt), (30 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (22 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (66 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (10 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_EndTxTransfer), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA), (160 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (120 bytes).
    Removing bsp_system.o(.rev16_text), (4 bytes).
    Removing bsp_system.o(.revsh_text), (4 bytes).
    Removing bsp_system.o(.rrx_text), (6 bytes).
    Removing bsp_system.o(i.Enable_Sync_Update), (20 bytes).
    Removing bsp_system.o(i.Set_CH0_Ready), (28 bytes).
    Removing bsp_system.o(i.Set_CH1_Ready), (28 bytes).
    Removing bsp_system.o(i.Update_Amplitude_CH0), (36 bytes).
    Removing bsp_system.o(i.Update_Amplitude_CH1), (36 bytes).
    Removing bsp_system.o(i.Update_Dual_Channel_Sync), (172 bytes).
    Removing bsp_system.o(i.Update_Frequency_CH0), (36 bytes).
    Removing bsp_system.o(i.Update_Frequency_CH1), (36 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing delay.o(i.delay_init), (44 bytes).
    Removing delay.o(i.delay_ms), (52 bytes).
    Removing delay.o(i.delay_us), (52 bytes).
    Removing delay.o(i.delay_xms), (52 bytes).
    Removing delay.o(.data), (4 bytes).
    Removing measurement.o(.rev16_text), (4 bytes).
    Removing measurement.o(.revsh_text), (4 bytes).
    Removing measurement.o(.rrx_text), (6 bytes).
    Removing measurement.o(i.Measurement_Disable), (20 bytes).
    Removing measurement.o(i.Measurement_Get_Amplitude), (12 bytes).
    Removing measurement.o(i.Measurement_Init), (28 bytes).
    Removing ad9959.o(.rev16_text), (4 bytes).
    Removing ad9959.o(.revsh_text), (4 bytes).
    Removing ad9959.o(.rrx_text), (6 bytes).
    Removing ad9959.o(i.AD9959_P0_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P0_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P1_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P1_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P2_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P2_L), (16 bytes).
    Removing ad9959.o(i.AD9959_P3_H), (16 bytes).
    Removing ad9959.o(i.AD9959_P3_L), (16 bytes).
    Removing ad9959.o(i.AD9959_PDC_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO1_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO1_L), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO2_H), (16 bytes).
    Removing ad9959.o(i.AD9959_SDIO2_L), (16 bytes).
    Removing ad9959.o(i.AD9959_Set_Amp_NoUpdate), (10 bytes).
    Removing ad9959.o(i.AD9959_Set_Fre_NoUpdate), (44 bytes).
    Removing ad9959.o(i.AD9959_Set_Pha_NoUpdate), (28 bytes).
    Removing ad9959.o(i.AD9959_Single_Output_NoUpdate), (46 bytes).
    Removing ad9959.o(i.AD9959_Sweep_Phase), (220 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Amp), (148 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Fre), (260 bytes).
    Removing ad9959.o(i.AD9959__Sweep_Trigger), (42 bytes).
    Removing ad9959.o(i.AD9959_proc), (84 bytes).
    Removing ad9959.o(i.Update_CH0_Frequency_With_Delay_Linkage), (44 bytes).
    Removing ad9959.o(i.Update_CH1_Frequency_With_Delay_Linkage), (44 bytes).
    Removing ad9959.o(i.Update_Channel_Output_Sync), (148 bytes).
    Removing ad9959.o(.data), (44 bytes).
    Removing ad9959.o(.data), (1 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing ad9959.o(.data), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.I2C_Start), (68 bytes).
    Removing oled.o(i.I2C_Stop), (52 bytes).
    Removing oled.o(i.I2C_WaitAck), (56 bytes).
    Removing oled.o(i.IIC_delay), (10 bytes).
    Removing oled.o(i.OLED_Clear), (44 bytes).
    Removing oled.o(i.OLED_ColorTurn), (24 bytes).
    Removing oled.o(i.OLED_DisPlay_Off), (30 bytes).
    Removing oled.o(i.OLED_DisPlay_On), (30 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_DrawCircle), (196 bytes).
    Removing oled.o(i.OLED_DrawLine), (156 bytes).
    Removing oled.o(i.OLED_DrawPoint), (44 bytes).
    Removing oled.o(i.OLED_Init), (240 bytes).
    Removing oled.o(i.OLED_Pow), (16 bytes).
    Removing oled.o(i.OLED_Refresh), (104 bytes).
    Removing oled.o(i.OLED_ScrollDisplay), (152 bytes).
    Removing oled.o(i.OLED_ShowChar), (228 bytes).
    Removing oled.o(i.OLED_ShowChinese), (208 bytes).
    Removing oled.o(i.OLED_ShowNum), (110 bytes).
    Removing oled.o(i.OLED_ShowPicture), (156 bytes).
    Removing oled.o(i.OLED_ShowString), (60 bytes).
    Removing oled.o(i.OLED_WR_Byte), (54 bytes).
    Removing oled.o(i.Send_Byte), (84 bytes).
    Removing oled.o(.bss), (1152 bytes).
    Removing oled.o(.constdata), (7696 bytes).
    Removing my_hmi.o(.rev16_text), (4 bytes).
    Removing my_hmi.o(.revsh_text), (4 bytes).
    Removing my_hmi.o(.rrx_text), (6 bytes).
    Removing my_hmi.o(i.HMI_Wave_Clear), (32 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Fast), (100 bytes).
    Removing my_hmi.o(i.HMI_Write_Wave_Low), (44 bytes).
    Removing my_usart.o(.rev16_text), (4 bytes).
    Removing my_usart.o(.revsh_text), (4 bytes).
    Removing my_usart.o(.rrx_text), (6 bytes).
    Removing my_usart.o(i.Get_CH0_Modulation_Depth), (12 bytes).
    Removing my_usart.o(i.Get_CH0_Time_Delay), (12 bytes).
    Removing my_usart.o(i.Get_CH1_Modulation_Depth), (12 bytes).
    Removing my_usart.o(i.Get_CH1_Time_Delay), (12 bytes).
    Removing my_usart.o(i.USART1_Init_IT), (28 bytes).
    Removing my_usart.o(i.USART3_Init_IT), (28 bytes).
    Removing my_usart.o(.bss), (256 bytes).
    Removing my_usart.o(.data), (2 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart.o(.data), (1 bytes).
    Removing my_usart_pack.o(.rev16_text), (4 bytes).
    Removing my_usart_pack.o(.revsh_text), (4 bytes).
    Removing my_usart_pack.o(.rrx_text), (6 bytes).
    Removing my_usart_pack.o(i.ParseDataToVariables), (152 bytes).
    Removing my_usart_pack.o(i.ParseFrame), (66 bytes).
    Removing my_usart_pack.o(i.PrepareFrame), (220 bytes).
    Removing my_usart_pack.o(i.SendFrame), (20 bytes).
    Removing my_usart_pack.o(i.SetParseTemplate), (52 bytes).
    Removing my_usart_pack.o(.bss), (52 bytes).
    Removing my_usart_pack.o(.data), (2 bytes).

563 unused section(s) (total 47037 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_adc.c 0x00000000   Number         0  stm32f4xx_ll_adc.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/string.c                         0x00000000   Number         0  strlen.o ABSOLUTE
    ../fplib/basic.s                         0x00000000   Number         0  basic.o ABSOLUTE
    ../fplib/daddsub.s                       0x00000000   Number         0  daddsub_clz.o ABSOLUTE
    ../fplib/dcheck1.s                       0x00000000   Number         0  dcheck1.o ABSOLUTE
    ../fplib/dcmpi.s                         0x00000000   Number         0  dcmpi.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dfix.s                          0x00000000   Number         0  dfix.o ABSOLUTE
    ../fplib/dfixu.s                         0x00000000   Number         0  dfixu.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dleqf.s                         0x00000000   Number         0  dleqf.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/drleqf.s                        0x00000000   Number         0  drleqf.o ABSOLUTE
    ../fplib/dsqrt.s                         0x00000000   Number         0  dsqrt_umaal.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/retnan.s                        0x00000000   Number         0  retnan.o ABSOLUTE
    ../fplib/scalbn.s                        0x00000000   Number         0  scalbn.o ABSOLUTE
    ../fplib/trapv.s                         0x00000000   Number         0  trapv.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow.o ABSOLUTE
    ../mathlib/pow.c                         0x00000000   Number         0  pow_x.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\Core\Src\adc.c                        0x00000000   Number         0  adc.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc.c 0x00000000   Number         0  stm32f4xx_hal_adc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_adc_ex.c 0x00000000   Number         0  stm32f4xx_hal_adc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\\my_code\\bsp\\Src\\bsp_system.c      0x00000000   Number         0  bsp_system.o ABSOLUTE
    ..\\my_code\\logic\\Src\\delay.c         0x00000000   Number         0  delay.o ABSOLUTE
    ..\\my_code\\logic\\Src\\measurement.c   0x00000000   Number         0  measurement.o ABSOLUTE
    ..\\my_code\\peripher\\AD9959\\Src\\AD9959.c 0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\\my_code\\peripher\\OLED\\oled.c      0x00000000   Number         0  oled.o ABSOLUTE
    ..\\my_code\\usart\\Src\\my_hmi.c        0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\\my_code\\usart\\Src\\my_usart.c      0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\\my_code\\usart\\Src\\my_usart_pack.c 0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    ..\my_code\bsp\Src\bsp_system.c          0x00000000   Number         0  bsp_system.o ABSOLUTE
    ..\my_code\logic\Src\delay.c             0x00000000   Number         0  delay.o ABSOLUTE
    ..\my_code\logic\Src\measurement.c       0x00000000   Number         0  measurement.o ABSOLUTE
    ..\my_code\peripher\AD9959\Src\AD9959.c  0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\my_code\peripher\OLED\oled.c          0x00000000   Number         0  oled.o ABSOLUTE
    ..\my_code\usart\Src\my_hmi.c            0x00000000   Number         0  my_hmi.o ABSOLUTE
    ..\my_code\usart\Src\my_usart.c          0x00000000   Number         0  my_usart.o ABSOLUTE
    ..\my_code\usart\Src\my_usart_pack.c     0x00000000   Number         0  my_usart_pack.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x080001c4   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x080001e0   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001fc   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001fc   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000202   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000208   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800020e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000214   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800021a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000220   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800022a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000230   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000236   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800023c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000242   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000248   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800024e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000254   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800025a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000260   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x08000266   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000270   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x08000276   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x0800027c   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x08000282   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000288   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x0800028c   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x0800028e   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000292   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000292   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x08000298   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x08000298   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002a4   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002a4   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002ae   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002ae   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002b0   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000007      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    .ARM.Collect$$libshutdown$$0000000A      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    .ARM.Collect$$libshutdown$$0000000C      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000F      0x080002b2   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    .ARM.Collect$$libshutdown$$00000010      0x080002b2   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    .ARM.Collect$$rtentry$$00000000          0x080002b4   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002b4   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002b4   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x080002ba   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x080002ba   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x080002be   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x080002be   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x080002c6   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x080002c8   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x080002c8   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x080002cc   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x080002d4   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x080002d4   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x08000314   Section      238  lludivv7m.o(.text)
    .text                                    0x08000404   Section        0  vsnprintf.o(.text)
    .text                                    0x08000438   Section        0  strlen.o(.text)
    .text                                    0x08000476   Section       78  rt_memclr_w.o(.text)
    .text                                    0x080004c4   Section      128  strcmpv7m.o(.text)
    .text                                    0x08000544   Section        0  heapauxi.o(.text)
    .text                                    0x0800054a   Section        0  _rserrno.o(.text)
    .text                                    0x08000560   Section        0  _printf_pad.o(.text)
    .text                                    0x080005ae   Section        0  _printf_truncate.o(.text)
    .text                                    0x080005d2   Section        0  _printf_str.o(.text)
    .text                                    0x08000624   Section        0  _printf_dec.o(.text)
    .text                                    0x0800069c   Section        0  _printf_charcount.o(.text)
    .text                                    0x080006c4   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x080006c5   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x080006f4   Section        0  _sputc.o(.text)
    .text                                    0x080006fe   Section        0  _snputc.o(.text)
    .text                                    0x08000710   Section        0  _printf_wctomb.o(.text)
    .text                                    0x080007cc   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000848   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000849   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x080008b8   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x080008b9   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x0800094c   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000ad4   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000adc   Section      138  lludiv10.o(.text)
    .text                                    0x08000b66   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08000c18   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08000c1b   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001038   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001334   Section        0  _printf_char.o(.text)
    .text                                    0x08001360   Section        0  _printf_wchar.o(.text)
    .text                                    0x0800138c   Section        0  _wcrtomb.o(.text)
    .text                                    0x080013cc   Section        8  libspace.o(.text)
    .text                                    0x080013d4   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001420   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001430   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001438   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x080014b8   Section        0  bigflt0.o(.text)
    .text                                    0x0800159c   Section        0  exit.o(.text)
    .text                                    0x080015b0   Section        0  sys_exit.o(.text)
    .text                                    0x080015bc   Section        2  use_no_semi.o(.text)
    .text                                    0x080015be   Section        0  indicate_semi.o(.text)
    CL$$btod_d2e                             0x080015be   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080015fc   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001642   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x080016a2   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x080019da   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08001ab6   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08001ae0   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08001b0a   Section      580  btod.o(CL$$btod_mult_common)
    i.AD9959_CS_H                            0x08001d50   Section        0  ad9959.o(i.AD9959_CS_H)
    i.AD9959_CS_L                            0x08001d60   Section        0  ad9959.o(i.AD9959_CS_L)
    i.AD9959_Ch                              0x08001d70   Section        0  ad9959.o(i.AD9959_Ch)
    i.AD9959_IO_UpDate                       0x08001d98   Section        0  ad9959.o(i.AD9959_IO_UpDate)
    i.AD9959_Init                            0x08001dc0   Section        0  ad9959.o(i.AD9959_Init)
    i.AD9959_PDC_L                           0x08001dfc   Section        0  ad9959.o(i.AD9959_PDC_L)
    i.AD9959_RST_H                           0x08001e0c   Section        0  ad9959.o(i.AD9959_RST_H)
    i.AD9959_RST_L                           0x08001e1c   Section        0  ad9959.o(i.AD9959_RST_L)
    i.AD9959_Reset                           0x08001e2c   Section        0  ad9959.o(i.AD9959_Reset)
    i.AD9959_SCK_H                           0x08001e4c   Section        0  ad9959.o(i.AD9959_SCK_H)
    i.AD9959_SCK_L                           0x08001e5c   Section        0  ad9959.o(i.AD9959_SCK_L)
    i.AD9959_SDIO0_H                         0x08001e6c   Section        0  ad9959.o(i.AD9959_SDIO0_H)
    i.AD9959_SDIO0_L                         0x08001e7c   Section        0  ad9959.o(i.AD9959_SDIO0_L)
    i.AD9959_SDIO3_H                         0x08001e8c   Section        0  ad9959.o(i.AD9959_SDIO3_H)
    i.AD9959_SDIO3_L                         0x08001e9c   Section        0  ad9959.o(i.AD9959_SDIO3_L)
    i.AD9959_Set_Amp                         0x08001eac   Section        0  ad9959.o(i.AD9959_Set_Amp)
    i.AD9959_Set_Fre                         0x08001ec0   Section        0  ad9959.o(i.AD9959_Set_Fre)
    i.AD9959_Set_Pha                         0x08001ef0   Section        0  ad9959.o(i.AD9959_Set_Pha)
    i.AD9959_Single_Output                   0x08001f14   Section        0  ad9959.o(i.AD9959_Single_Output)
    i.AD9959_Start                           0x08001f42   Section        0  ad9959.o(i.AD9959_Start)
    i.AD9959_UP_H                            0x08001f5c   Section        0  ad9959.o(i.AD9959_UP_H)
    i.AD9959_UP_L                            0x08001f6c   Section        0  ad9959.o(i.AD9959_UP_L)
    i.AD9959_WByte                           0x08001f7c   Section        0  ad9959.o(i.AD9959_WByte)
    i.AD9959_WRrg                            0x08001fac   Section        0  ad9959.o(i.AD9959_WRrg)
    i.ADC_DMAConvCplt                        0x08001ff0   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    ADC_DMAConvCplt                          0x08001ff1   Thumb Code   110  stm32f4xx_hal_adc.o(i.ADC_DMAConvCplt)
    i.ADC_DMAError                           0x0800205e   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    ADC_DMAError                             0x0800205f   Thumb Code    22  stm32f4xx_hal_adc.o(i.ADC_DMAError)
    i.ADC_DMAHalfConvCplt                    0x08002074   Section        0  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    ADC_DMAHalfConvCplt                      0x08002075   Thumb Code    10  stm32f4xx_hal_adc.o(i.ADC_DMAHalfConvCplt)
    i.ADC_Init                               0x08002080   Section        0  stm32f4xx_hal_adc.o(i.ADC_Init)
    ADC_Init                                 0x08002081   Thumb Code   284  stm32f4xx_hal_adc.o(i.ADC_Init)
    i.BusFault_Handler                       0x080021a8   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.Calculate_Phase_From_Delay             0x080021ac   Section        0  ad9959.o(i.Calculate_Phase_From_Delay)
    i.Check_And_Sync_Update                  0x08002200   Section        0  bsp_system.o(i.Check_And_Sync_Update)
    i.Check_UART1_Command                    0x08002238   Section        0  my_usart.o(i.Check_UART1_Command)
    i.Check_UART3_Command                    0x08002258   Section        0  my_usart.o(i.Check_UART3_Command)
    i.Convert_mV_to_AD9959_Amp               0x080022c8   Section        0  ad9959.o(i.Convert_mV_to_AD9959_Amp)
    i.DMA2_Stream0_IRQHandler                0x080022f8   Section        0  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x08002304   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x08002305   Thumb Code    34  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x0800232c   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x0800232d   Thumb Code    84  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08002380   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08002381   Thumb Code    40  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080023a8   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Disable_Sync_Update                    0x080023ac   Section        0  bsp_system.o(i.Disable_Sync_Update)
    i.Display_Relative_Delay_Info            0x080023bc   Section        0  my_usart.o(i.Display_Relative_Delay_Info)
    i.Error_Handler                          0x08002400   Section        0  main.o(i.Error_Handler)
    i.HAL_ADC_ConfigChannel                  0x08002404   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    i.HAL_ADC_ConvCpltCallback               0x08002550   Section        0  measurement.o(i.HAL_ADC_ConvCpltCallback)
    i.HAL_ADC_ConvHalfCpltCallback           0x0800259c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    i.HAL_ADC_ErrorCallback                  0x0800259e   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    i.HAL_ADC_Init                           0x080025a0   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    i.HAL_ADC_MspInit                        0x080025f4   Section        0  adc.o(i.HAL_ADC_MspInit)
    i.HAL_ADC_Start_DMA                      0x0800268c   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    i.HAL_ADC_Stop_DMA                       0x080027e4   Section        0  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    i.HAL_DMA_Abort                          0x08002850   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x080028e2   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002908   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08002aa8   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08002b7c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08002bec   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x08002c10   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_WritePin                      0x08002e00   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08002e0c   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08002e18   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08002e28   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08002e5c   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08002e9c   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08002ecc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08002ee8   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08002f28   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08002f4c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetPCLK1Freq                   0x08003080   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x080030a0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x080030c0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x08003120   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x0800348c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_MasterConfigSynchronization  0x080034b4   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x08003544   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x080035a0   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start                     0x080035c4   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    i.HAL_TIM_ConfigClockSource              0x0800363c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_UARTEx_RxEventCallback             0x08003718   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_ErrorCallback                 0x0800371a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800371c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x0800399c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08003a00   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_IT                    0x08003af8   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    i.HAL_UART_RxCpltCallback                0x08003b14   Section        0  my_usart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_Transmit                      0x08003bd0   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08003c70   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HMI_Debug_Print                        0x08003c74   Section        0  my_hmi.o(i.HMI_Debug_Print)
    i.HMI_Send_Float                         0x08003cac   Section        0  my_hmi.o(i.HMI_Send_Float)
    i.HMI_Send_Int                           0x08003d18   Section        0  my_hmi.o(i.HMI_Send_Int)
    i.HMI_Send_String                        0x08003d38   Section        0  my_hmi.o(i.HMI_Send_String)
    i.HardFault_Handler                      0x08003d58   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_ADC1_Init                           0x08003d5c   Section        0  adc.o(i.MX_ADC1_Init)
    i.MX_DMA_Init                            0x08003dc0   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08003dec   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_TIM2_Init                           0x08003f14   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_USART1_UART_Init                    0x08003f7c   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MX_USART2_UART_Init                    0x08003fb4   Section        0  usart.o(i.MX_USART2_UART_Init)
    i.MX_USART3_UART_Init                    0x08003fec   Section        0  usart.o(i.MX_USART3_UART_Init)
    i.Measurement_Enable                     0x08004024   Section        0  measurement.o(i.Measurement_Enable)
    i.Measurement_Process_DC                 0x08004050   Section        0  measurement.o(i.Measurement_Process_DC)
    i.Measurement_Reset_Filter               0x08004154   Section        0  measurement.o(i.Measurement_Reset_Filter)
    i.MemManage_Handler                      0x08004164   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08004166   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.Parse_Command_UART1                    0x08004168   Section        0  my_usart.o(i.Parse_Command_UART1)
    i.PendSV_Handler                         0x08005068   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Process_System_State                   0x0800506c   Section        0  bsp_system.o(i.Process_System_State)
    i.SVC_Handler                            0x080050b8   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Send_FPGA_Command                      0x080050bc   Section        0  my_usart.o(i.Send_FPGA_Command)
    i.Send_Peak_Value_To_FPGA                0x080050e8   Section        0  my_usart.o(i.Send_Peak_Value_To_FPGA)
    i.Set_Parameter_Changed                  0x08005154   Section        0  bsp_system.o(i.Set_Parameter_Changed)
    i.SysTick_Handler                        0x0800516c   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08005170   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08005204   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.System_Init_Sync_Control               0x08005214   Section        0  main.o(i.System_Init_Sync_Control)
    i.System_Init_Time_Delay                 0x08005290   Section        0  main.o(i.System_Init_Time_Delay)
    i.TIM_Base_SetConfig                     0x080052a0   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_ETR_SetConfig                      0x08005370   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x08005384   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x08005385   Thumb Code    16  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08005394   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08005395   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x080053b6   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x080053b7   Thumb Code    36  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART_DMAAbortOnError                   0x080053da   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080053db   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_EndRxTransfer                     0x080053e8   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x080053e9   Thumb Code    78  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_Receive_IT                        0x08005436   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08005437   Thumb Code   194  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080054f8   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080054f9   Thumb Code   258  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_IT                  0x08005604   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    i.UART_WaitOnFlagUntilTimeout            0x0800563a   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x0800563b   Thumb Code   114  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x080056ac   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.Update_CH0_With_Time_Delay             0x080056b8   Section        0  ad9959.o(i.Update_CH0_With_Time_Delay)
    i.Update_CH1_With_Time_Delay             0x080056f0   Section        0  ad9959.o(i.Update_CH1_With_Time_Delay)
    i.Update_Channel_Output                  0x08005728   Section        0  ad9959.o(i.Update_Channel_Output)
    i.UsageFault_Handler                     0x08005790   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08005792   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__NVIC_SetPriority                     0x080057c2   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x080057c3   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_pow                           0x080057e8   Section        0  pow.o(i.__hardfp_pow)
    i.__kernel_poly                          0x08006438   Section        0  poly.o(i.__kernel_poly)
    i.__mathlib_dbl_divzero                  0x08006530   Section        0  dunder.o(i.__mathlib_dbl_divzero)
    i.__mathlib_dbl_infnan2                  0x08006560   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x08006578   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_overflow                 0x08006598   Section        0  dunder.o(i.__mathlib_dbl_overflow)
    i.__mathlib_dbl_underflow                0x080065b8   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i._is_digit                              0x080065d8   Section        0  __printf_wp.o(i._is_digit)
    i.fabs                                   0x080065e6   Section        0  fabs.o(i.fabs)
    i.main                                   0x08006600   Section        0  main.o(i.main)
    i.my_printf                              0x080067e4   Section        0  my_usart.o(i.my_printf)
    i.sqrt                                   0x08006816   Section        0  sqrt.o(i.sqrt)
    locale$$code                             0x08006884   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080068b0   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$basic                              0x080068dc   Section       24  basic.o(x$fpl$basic)
    $v0                                      0x080068dc   Number         0  basic.o(x$fpl$basic)
    x$fpl$dadd                               0x080068f4   Section      336  daddsub_clz.o(x$fpl$dadd)
    $v0                                      0x080068f4   Number         0  daddsub_clz.o(x$fpl$dadd)
    _dadd1                                   0x08006905   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    x$fpl$dcheck1                            0x08006a44   Section       16  dcheck1.o(x$fpl$dcheck1)
    $v0                                      0x08006a44   Number         0  dcheck1.o(x$fpl$dcheck1)
    x$fpl$dcmpinf                            0x08006a54   Section       24  dcmpi.o(x$fpl$dcmpinf)
    $v0                                      0x08006a54   Number         0  dcmpi.o(x$fpl$dcmpinf)
    x$fpl$ddiv                               0x08006a6c   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08006a6c   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08006a73   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dfix                               0x08006d1c   Section       94  dfix.o(x$fpl$dfix)
    $v0                                      0x08006d1c   Number         0  dfix.o(x$fpl$dfix)
    x$fpl$dfixu                              0x08006d7c   Section       90  dfixu.o(x$fpl$dfixu)
    $v0                                      0x08006d7c   Number         0  dfixu.o(x$fpl$dfixu)
    x$fpl$dflt                               0x08006dd6   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x08006dd6   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dfltu                              0x08006e04   Section       38  dflt_clz.o(x$fpl$dfltu)
    $v0                                      0x08006e04   Number         0  dflt_clz.o(x$fpl$dfltu)
    x$fpl$dleqf                              0x08006e2c   Section      120  dleqf.o(x$fpl$dleqf)
    $v0                                      0x08006e2c   Number         0  dleqf.o(x$fpl$dleqf)
    x$fpl$dmul                               0x08006ea4   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08006ea4   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08006ff8   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08006ff8   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08007094   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08007094   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$drleqf                             0x080070a0   Section      108  drleqf.o(x$fpl$drleqf)
    $v0                                      0x080070a0   Number         0  drleqf.o(x$fpl$drleqf)
    x$fpl$drsb                               0x0800710c   Section       22  daddsub_clz.o(x$fpl$drsb)
    $v0                                      0x0800710c   Number         0  daddsub_clz.o(x$fpl$drsb)
    x$fpl$dsqrt                              0x08007124   Section      408  dsqrt_umaal.o(x$fpl$dsqrt)
    $v0                                      0x08007124   Number         0  dsqrt_umaal.o(x$fpl$dsqrt)
    x$fpl$dsub                               0x080072bc   Section      468  daddsub_clz.o(x$fpl$dsub)
    $v0                                      0x080072bc   Number         0  daddsub_clz.o(x$fpl$dsub)
    _dsub1                                   0x080072cd   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    x$fpl$f2d                                0x08007490   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08007490   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x080074e6   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x080074e6   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x08007572   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x08007572   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$printf1                            0x0800757c   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800757c   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08007580   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x08007580   Number         0  printf2.o(x$fpl$printf2)
    x$fpl$retnan                             0x08007584   Section      100  retnan.o(x$fpl$retnan)
    $v0                                      0x08007584   Number         0  retnan.o(x$fpl$retnan)
    x$fpl$scalbn                             0x080075e8   Section       92  scalbn.o(x$fpl$scalbn)
    $v0                                      0x080075e8   Number         0  scalbn.o(x$fpl$scalbn)
    x$fpl$trapveneer                         0x08007644   Section       48  trapv.o(x$fpl$trapveneer)
    $v0                                      0x08007644   Number         0  trapv.o(x$fpl$trapveneer)
    .constdata                               0x08007674   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x08007674   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x08007674   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800767c   Section       16  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800768c   Section        8  system_stm32f4xx.o(.constdata)
    .constdata                               0x08007698   Section      136  pow.o(.constdata)
    bp                                       0x08007698   Data          16  pow.o(.constdata)
    dp_h                                     0x080076a8   Data          16  pow.o(.constdata)
    dp_l                                     0x080076b8   Data          16  pow.o(.constdata)
    L                                        0x080076c8   Data          48  pow.o(.constdata)
    P                                        0x080076f8   Data          40  pow.o(.constdata)
    .constdata                               0x08007720   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08007720   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x08007728   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x08007728   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800773c   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x08007750   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x08007750   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08007768   Section        8  qnan.o(.constdata)
    .constdata                               0x08007770   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x08007770   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x08007783   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x08007798   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x08007798   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x080077d4   Data          64  bigflt0.o(.constdata)
    .conststring                             0x0800782c   Section       94  my_usart.o(.conststring)
    locale$$data                             0x080078ac   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080078b0   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080078b8   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080078c4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080078c6   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080078c7   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x080078c8   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x080078c8   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x080078cc   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080078d4   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080079d8   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       12  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       32  bsp_system.o(.data)
    last_ch0_flag                            0x20000017   Data           1  bsp_system.o(.data)
    last_ch1_flag                            0x20000018   Data           1  bsp_system.o(.data)
    .data                                    0x20000030   Section        4  bsp_system.o(.data)
    .data                                    0x20000034   Section        1  bsp_system.o(.data)
    .data                                    0x20000036   Section        2  bsp_system.o(.data)
    .data                                    0x20000038   Section        2  bsp_system.o(.data)
    .data                                    0x2000003c   Section       12  measurement.o(.data)
    pp_buffer_index                          0x2000003c   Data           1  measurement.o(.data)
    pp_sample_count                          0x2000003d   Data           1  measurement.o(.data)
    measured_amplitude                       0x20000040   Data           4  measurement.o(.data)
    dma_complete_counter                     0x20000044   Data           4  measurement.o(.data)
    .data                                    0x20000048   Section       33  ad9959.o(.data)
    .data                                    0x2000006a   Section       10  my_usart.o(.data)
    .bss                                     0x20000074   Section      168  adc.o(.bss)
    .bss                                     0x2000011c   Section       72  tim.o(.bss)
    .bss                                     0x20000164   Section      216  usart.o(.bss)
    .bss                                     0x2000023c   Section      160  measurement.o(.bss)
    pp_buffer                                0x2000023c   Data          32  measurement.o(.bss)
    adc_buff                                 0x2000025c   Data         128  measurement.o(.bss)
    .bss                                     0x200002dc   Section      512  my_usart.o(.bss)
    .bss                                     0x200004dc   Section       96  libspace.o(.bss)
    HEAP                                     0x20000540   Section      512  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x20000540   Data         512  startup_stm32f407xx.o(HEAP)
    STACK                                    0x20000740   Section     1024  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x20000740   Data        1024  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x20000b40   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x080001c5   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x080001e1   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001fd   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001fd   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000203   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000209   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800020f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000215   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800021b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000221   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800022b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000231   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000237   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800023d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000243   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000249   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800024f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000255   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800025b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000261   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x08000267   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000271   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x08000277   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x0800027d   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x08000283   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000289   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x0800028d   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x0800028f   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000293   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x08000299   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002a5   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002af   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002b1   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000007)
    __rt_lib_shutdown_heap_1                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F)
    __rt_lib_shutdown_return                 0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000010)
    __rt_lib_shutdown_signal_1               0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A)
    __rt_lib_shutdown_stdio_1                0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_user_alloc_1           0x080002b3   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_entry                               0x080002b5   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002b5   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002b5   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x080002bb   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x080002bf   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x080002c7   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x080002c9   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x080002cd   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x080002d5   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080002ef   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x080002f1   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x08000315   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000315   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x08000405   Thumb Code    48  vsnprintf.o(.text)
    strlen                                   0x08000439   Thumb Code    62  strlen.o(.text)
    __aeabi_memclr4                          0x08000477   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x08000477   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x08000477   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x0800047b   Thumb Code     0  rt_memclr_w.o(.text)
    strcmp                                   0x080004c5   Thumb Code   128  strcmpv7m.o(.text)
    __use_two_region_memory                  0x08000545   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x08000547   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08000549   Thumb Code     2  heapauxi.o(.text)
    __read_errno                             0x0800054b   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x08000555   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x08000561   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x0800058d   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x080005af   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x080005c1   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x080005d3   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000625   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x0800069d   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x080006cf   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x080006f5   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x080006ff   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000711   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x080007cd   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000849   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x0800088b   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x080008a3   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x080008b9   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x0800090f   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x0800092b   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000937   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x0800094d   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __aeabi_errno_addr                       0x08000ad5   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000ad5   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000ad5   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    _ll_udiv10                               0x08000add   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08000b67   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08000c19   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08000dcb   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08001039   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08001335   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001349   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001359   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08001361   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001375   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001385   Thumb Code     8  _printf_wchar.o(.text)
    _wcrtomb                                 0x0800138d   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x080013cd   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080013cd   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080013cd   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080013d5   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001421   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001431   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001439   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x080014b9   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x0800159d   Thumb Code    18  exit.o(.text)
    _sys_exit                                0x080015b1   Thumb Code     8  sys_exit.o(.text)
    __I$use$semihosting                      0x080015bd   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x080015bd   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x080015bf   Thumb Code     0  indicate_semi.o(.text)
    _btod_d2e                                0x080015bf   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080015fd   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001643   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x080016a3   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x080019db   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08001ab7   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08001ae1   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08001b0b   Thumb Code   580  btod.o(CL$$btod_mult_common)
    AD9959_CS_H                              0x08001d51   Thumb Code    10  ad9959.o(i.AD9959_CS_H)
    AD9959_CS_L                              0x08001d61   Thumb Code    10  ad9959.o(i.AD9959_CS_L)
    AD9959_Ch                                0x08001d71   Thumb Code    40  ad9959.o(i.AD9959_Ch)
    AD9959_IO_UpDate                         0x08001d99   Thumb Code    40  ad9959.o(i.AD9959_IO_UpDate)
    AD9959_Init                              0x08001dc1   Thumb Code    58  ad9959.o(i.AD9959_Init)
    AD9959_PDC_L                             0x08001dfd   Thumb Code    10  ad9959.o(i.AD9959_PDC_L)
    AD9959_RST_H                             0x08001e0d   Thumb Code    10  ad9959.o(i.AD9959_RST_H)
    AD9959_RST_L                             0x08001e1d   Thumb Code    12  ad9959.o(i.AD9959_RST_L)
    AD9959_Reset                             0x08001e2d   Thumb Code    30  ad9959.o(i.AD9959_Reset)
    AD9959_SCK_H                             0x08001e4d   Thumb Code    10  ad9959.o(i.AD9959_SCK_H)
    AD9959_SCK_L                             0x08001e5d   Thumb Code    10  ad9959.o(i.AD9959_SCK_L)
    AD9959_SDIO0_H                           0x08001e6d   Thumb Code    10  ad9959.o(i.AD9959_SDIO0_H)
    AD9959_SDIO0_L                           0x08001e7d   Thumb Code    10  ad9959.o(i.AD9959_SDIO0_L)
    AD9959_SDIO3_H                           0x08001e8d   Thumb Code    10  ad9959.o(i.AD9959_SDIO3_H)
    AD9959_SDIO3_L                           0x08001e9d   Thumb Code    10  ad9959.o(i.AD9959_SDIO3_L)
    AD9959_Set_Amp                           0x08001ead   Thumb Code    20  ad9959.o(i.AD9959_Set_Amp)
    AD9959_Set_Fre                           0x08001ec1   Thumb Code    38  ad9959.o(i.AD9959_Set_Fre)
    AD9959_Set_Pha                           0x08001ef1   Thumb Code    32  ad9959.o(i.AD9959_Set_Pha)
    AD9959_Single_Output                     0x08001f15   Thumb Code    46  ad9959.o(i.AD9959_Single_Output)
    AD9959_Start                             0x08001f43   Thumb Code    26  ad9959.o(i.AD9959_Start)
    AD9959_UP_H                              0x08001f5d   Thumb Code    10  ad9959.o(i.AD9959_UP_H)
    AD9959_UP_L                              0x08001f6d   Thumb Code    10  ad9959.o(i.AD9959_UP_L)
    AD9959_WByte                             0x08001f7d   Thumb Code    48  ad9959.o(i.AD9959_WByte)
    AD9959_WRrg                              0x08001fad   Thumb Code    64  ad9959.o(i.AD9959_WRrg)
    BusFault_Handler                         0x080021a9   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    Calculate_Phase_From_Delay               0x080021ad   Thumb Code    72  ad9959.o(i.Calculate_Phase_From_Delay)
    Check_And_Sync_Update                    0x08002201   Thumb Code    50  bsp_system.o(i.Check_And_Sync_Update)
    Check_UART1_Command                      0x08002239   Thumb Code    24  my_usart.o(i.Check_UART1_Command)
    Check_UART3_Command                      0x08002259   Thumb Code    36  my_usart.o(i.Check_UART3_Command)
    Convert_mV_to_AD9959_Amp                 0x080022c9   Thumb Code    46  ad9959.o(i.Convert_mV_to_AD9959_Amp)
    DMA2_Stream0_IRQHandler                  0x080022f9   Thumb Code     6  stm32f4xx_it.o(i.DMA2_Stream0_IRQHandler)
    DebugMon_Handler                         0x080023a9   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Disable_Sync_Update                      0x080023ad   Thumb Code    12  bsp_system.o(i.Disable_Sync_Update)
    Display_Relative_Delay_Info              0x080023bd   Thumb Code    20  my_usart.o(i.Display_Relative_Delay_Info)
    Error_Handler                            0x08002401   Thumb Code     4  main.o(i.Error_Handler)
    HAL_ADC_ConfigChannel                    0x08002405   Thumb Code   316  stm32f4xx_hal_adc.o(i.HAL_ADC_ConfigChannel)
    HAL_ADC_ConvCpltCallback                 0x08002551   Thumb Code    52  measurement.o(i.HAL_ADC_ConvCpltCallback)
    HAL_ADC_ConvHalfCpltCallback             0x0800259d   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ConvHalfCpltCallback)
    HAL_ADC_ErrorCallback                    0x0800259f   Thumb Code     2  stm32f4xx_hal_adc.o(i.HAL_ADC_ErrorCallback)
    HAL_ADC_Init                             0x080025a1   Thumb Code    84  stm32f4xx_hal_adc.o(i.HAL_ADC_Init)
    HAL_ADC_MspInit                          0x080025f5   Thumb Code   132  adc.o(i.HAL_ADC_MspInit)
    HAL_ADC_Start_DMA                        0x0800268d   Thumb Code   306  stm32f4xx_hal_adc.o(i.HAL_ADC_Start_DMA)
    HAL_ADC_Stop_DMA                         0x080027e5   Thumb Code   108  stm32f4xx_hal_adc.o(i.HAL_ADC_Stop_DMA)
    HAL_DMA_Abort                            0x08002851   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x080028e3   Thumb Code    36  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002909   Thumb Code   412  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08002aa9   Thumb Code   206  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08002b7d   Thumb Code   110  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08002bed   Thumb Code    32  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x08002c11   Thumb Code   450  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08002e01   Thumb Code    10  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08002e0d   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08002e19   Thumb Code    12  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08002e29   Thumb Code    48  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08002e5d   Thumb Code    54  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08002e9d   Thumb Code    42  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08002ecd   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08002ee9   Thumb Code    60  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08002f29   Thumb Code    26  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08002f4d   Thumb Code   288  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08003081   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x080030a1   Thumb Code    20  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x080030c1   Thumb Code    88  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08003121   Thumb Code   856  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x0800348d   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_MasterConfigSynchronization    0x080034b5   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x08003545   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x080035a1   Thumb Code    30  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start                       0x080035c5   Thumb Code    90  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start)
    HAL_TIM_ConfigClockSource                0x0800363d   Thumb Code   220  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_UARTEx_RxEventCallback               0x08003719   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_ErrorCallback                   0x0800371b   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800371d   Thumb Code   636  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x0800399d   Thumb Code   100  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08003a01   Thumb Code   224  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_IT                      0x08003af9   Thumb Code    28  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT)
    HAL_UART_RxCpltCallback                  0x08003b15   Thumb Code   158  my_usart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_Transmit                        0x08003bd1   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08003c71   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HMI_Debug_Print                          0x08003c75   Thumb Code    34  my_hmi.o(i.HMI_Debug_Print)
    HMI_Send_Float                           0x08003cad   Thumb Code    78  my_hmi.o(i.HMI_Send_Float)
    HMI_Send_Int                             0x08003d19   Thumb Code    12  my_hmi.o(i.HMI_Send_Int)
    HMI_Send_String                          0x08003d39   Thumb Code    12  my_hmi.o(i.HMI_Send_String)
    HardFault_Handler                        0x08003d59   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MX_ADC1_Init                             0x08003d5d   Thumb Code    90  adc.o(i.MX_ADC1_Init)
    MX_DMA_Init                              0x08003dc1   Thumb Code    40  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08003ded   Thumb Code   276  gpio.o(i.MX_GPIO_Init)
    MX_TIM2_Init                             0x08003f15   Thumb Code    98  tim.o(i.MX_TIM2_Init)
    MX_USART1_UART_Init                      0x08003f7d   Thumb Code    48  usart.o(i.MX_USART1_UART_Init)
    MX_USART2_UART_Init                      0x08003fb5   Thumb Code    48  usart.o(i.MX_USART2_UART_Init)
    MX_USART3_UART_Init                      0x08003fed   Thumb Code    48  usart.o(i.MX_USART3_UART_Init)
    Measurement_Enable                       0x08004025   Thumb Code    26  measurement.o(i.Measurement_Enable)
    Measurement_Process_DC                   0x08004051   Thumb Code   208  measurement.o(i.Measurement_Process_DC)
    Measurement_Reset_Filter                 0x08004155   Thumb Code    10  measurement.o(i.Measurement_Reset_Filter)
    MemManage_Handler                        0x08004165   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08004167   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    Parse_Command_UART1                      0x08004169   Thumb Code  3714  my_usart.o(i.Parse_Command_UART1)
    PendSV_Handler                           0x08005069   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    Process_System_State                     0x0800506d   Thumb Code    70  bsp_system.o(i.Process_System_State)
    SVC_Handler                              0x080050b9   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    Send_FPGA_Command                        0x080050bd   Thumb Code    36  my_usart.o(i.Send_FPGA_Command)
    Send_Peak_Value_To_FPGA                  0x080050e9   Thumb Code    44  my_usart.o(i.Send_Peak_Value_To_FPGA)
    Set_Parameter_Changed                    0x08005155   Thumb Code    18  bsp_system.o(i.Set_Parameter_Changed)
    SysTick_Handler                          0x0800516d   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08005171   Thumb Code   140  main.o(i.SystemClock_Config)
    SystemInit                               0x08005205   Thumb Code    12  system_stm32f4xx.o(i.SystemInit)
    System_Init_Sync_Control                 0x08005215   Thumb Code    26  main.o(i.System_Init_Sync_Control)
    System_Init_Time_Delay                   0x08005291   Thumb Code    10  main.o(i.System_Init_Time_Delay)
    TIM_Base_SetConfig                       0x080052a1   Thumb Code   164  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_ETR_SetConfig                        0x08005371   Thumb Code    20  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    UART_Start_Receive_IT                    0x08005605   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT)
    USART1_IRQHandler                        0x080056ad   Thumb Code     6  stm32f4xx_it.o(i.USART1_IRQHandler)
    Update_CH0_With_Time_Delay               0x080056b9   Thumb Code    44  ad9959.o(i.Update_CH0_With_Time_Delay)
    Update_CH1_With_Time_Delay               0x080056f1   Thumb Code    44  ad9959.o(i.Update_CH1_With_Time_Delay)
    Update_Channel_Output                    0x08005729   Thumb Code    80  ad9959.o(i.Update_Channel_Output)
    UsageFault_Handler                       0x08005791   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08005793   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_pow                             0x080057e9   Thumb Code  3072  pow.o(i.__hardfp_pow)
    __kernel_poly                            0x08006439   Thumb Code   248  poly.o(i.__kernel_poly)
    __mathlib_dbl_divzero                    0x08006531   Thumb Code    28  dunder.o(i.__mathlib_dbl_divzero)
    __mathlib_dbl_infnan2                    0x08006561   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x08006579   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_overflow                   0x08006599   Thumb Code    24  dunder.o(i.__mathlib_dbl_overflow)
    __mathlib_dbl_underflow                  0x080065b9   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    _is_digit                                0x080065d9   Thumb Code    14  __printf_wp.o(i._is_digit)
    fabs                                     0x080065e7   Thumb Code    24  fabs.o(i.fabs)
    main                                     0x08006601   Thumb Code   264  main.o(i.main)
    my_printf                                0x080067e5   Thumb Code    50  my_usart.o(i.my_printf)
    sqrt                                     0x08006817   Thumb Code   110  sqrt.o(i.sqrt)
    _get_lc_numeric                          0x08006885   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080068b1   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_dneg                             0x080068dd   Thumb Code     0  basic.o(x$fpl$basic)
    _dneg                                    0x080068dd   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_fneg                             0x080068e3   Thumb Code     0  basic.o(x$fpl$basic)
    _fneg                                    0x080068e3   Thumb Code     6  basic.o(x$fpl$basic)
    _dabs                                    0x080068e9   Thumb Code     6  basic.o(x$fpl$basic)
    _fabs                                    0x080068ef   Thumb Code     6  basic.o(x$fpl$basic)
    __aeabi_dadd                             0x080068f5   Thumb Code     0  daddsub_clz.o(x$fpl$dadd)
    _dadd                                    0x080068f5   Thumb Code   332  daddsub_clz.o(x$fpl$dadd)
    __fpl_dcheck_NaN1                        0x08006a45   Thumb Code    10  dcheck1.o(x$fpl$dcheck1)
    __fpl_dcmp_Inf                           0x08006a55   Thumb Code    24  dcmpi.o(x$fpl$dcmpinf)
    __aeabi_ddiv                             0x08006a6d   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08006a6d   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_d2iz                             0x08006d1d   Thumb Code     0  dfix.o(x$fpl$dfix)
    _dfix                                    0x08006d1d   Thumb Code    94  dfix.o(x$fpl$dfix)
    __aeabi_d2uiz                            0x08006d7d   Thumb Code     0  dfixu.o(x$fpl$dfixu)
    _dfixu                                   0x08006d7d   Thumb Code    90  dfixu.o(x$fpl$dfixu)
    __aeabi_i2d                              0x08006dd7   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08006dd7   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_ui2d                             0x08006e05   Thumb Code     0  dflt_clz.o(x$fpl$dfltu)
    _dfltu                                   0x08006e05   Thumb Code    38  dflt_clz.o(x$fpl$dfltu)
    __aeabi_cdcmple                          0x08006e2d   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    _dcmple                                  0x08006e2d   Thumb Code   120  dleqf.o(x$fpl$dleqf)
    __fpl_dcmple_InfNaN                      0x08006e8f   Thumb Code     0  dleqf.o(x$fpl$dleqf)
    __aeabi_dmul                             0x08006ea5   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08006ea5   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08006ff9   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08007095   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_cdrcmple                         0x080070a1   Thumb Code     0  drleqf.o(x$fpl$drleqf)
    _drcmple                                 0x080070a1   Thumb Code   108  drleqf.o(x$fpl$drleqf)
    __aeabi_drsub                            0x0800710d   Thumb Code     0  daddsub_clz.o(x$fpl$drsb)
    _drsb                                    0x0800710d   Thumb Code    22  daddsub_clz.o(x$fpl$drsb)
    _dsqrt                                   0x08007125   Thumb Code   404  dsqrt_umaal.o(x$fpl$dsqrt)
    __aeabi_dsub                             0x080072bd   Thumb Code     0  daddsub_clz.o(x$fpl$dsub)
    _dsub                                    0x080072bd   Thumb Code   464  daddsub_clz.o(x$fpl$dsub)
    __aeabi_f2d                              0x08007491   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08007491   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x080074e7   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x08007573   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800757b   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800757b   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    _printf_fp_dec                           0x0800757d   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08007581   Thumb Code     4  printf2.o(x$fpl$printf2)
    __fpl_return_NaN                         0x08007585   Thumb Code   100  retnan.o(x$fpl$retnan)
    __ARM_scalbn                             0x080075e9   Thumb Code    92  scalbn.o(x$fpl$scalbn)
    __fpl_cmpreturn                          0x08007645   Thumb Code    48  trapv.o(x$fpl$trapveneer)
    __I$use$fp                               0x08007674   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x0800767c   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800768c   Data           8  system_stm32f4xx.o(.constdata)
    __mathlib_zero                           0x08007768   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0800788c   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080078ac   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080078d5   Data           0  lc_ctype_c.o(locale$$data)
    uwTickFreq                               0x20000000   Data           1  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTick                                   0x20000008   Data           4  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    system_state                             0x20000010   Data           1  bsp_system.o(.data)
    parameter_changed_flag                   0x20000011   Data           1  bsp_system.o(.data)
    adc_processing_flag                      0x20000012   Data           1  bsp_system.o(.data)
    fft_calculate_flag                       0x20000013   Data           1  bsp_system.o(.data)
    ch0_param_ready_flag                     0x20000014   Data           1  bsp_system.o(.data)
    ch1_param_ready_flag                     0x20000015   Data           1  bsp_system.o(.data)
    sync_update_enable                       0x20000016   Data           1  bsp_system.o(.data)
    amp_value_mv_ch0                         0x2000001a   Data           2  bsp_system.o(.data)
    phase_value_deg_ch0                      0x2000001c   Data           2  bsp_system.o(.data)
    amp_value_mv_ch1                         0x2000001e   Data           2  bsp_system.o(.data)
    phase_value_deg_ch1                      0x20000020   Data           2  bsp_system.o(.data)
    last_parameter_change_time               0x20000024   Data           4  bsp_system.o(.data)
    freq_value_ch0                           0x20000028   Data           4  bsp_system.o(.data)
    freq_value_ch1                           0x2000002c   Data           4  bsp_system.o(.data)
    last_measurement_time                    0x20000030   Data           4  bsp_system.o(.data)
    measurement_enable                       0x20000034   Data           1  bsp_system.o(.data)
    time_delay_ns_ch0                        0x20000036   Data           2  bsp_system.o(.data)
    time_delay_ns_ch1                        0x20000038   Data           2  bsp_system.o(.data)
    mdoe_flag                                0x20000048   Data           1  ad9959.o(.data)
    Phase                                    0x2000004a   Data           2  ad9959.o(.data)
    pid_vin                                  0x2000004c   Data           4  ad9959.o(.data)
    Reg_Len                                  0x20000050   Data          25  ad9959.o(.data)
    commandReceived1                         0x2000006a   Data           1  my_usart.o(.data)
    commandReceived3                         0x2000006b   Data           1  my_usart.o(.data)
    modulation_depth_ch0                     0x2000006c   Data           1  my_usart.o(.data)
    modulation_depth_ch1                     0x2000006d   Data           1  my_usart.o(.data)
    rxTemp1                                  0x2000006e   Data           1  my_usart.o(.data)
    rxTemp3                                  0x2000006f   Data           1  my_usart.o(.data)
    rxIndex1                                 0x20000070   Data           2  my_usart.o(.data)
    rxIndex3                                 0x20000072   Data           2  my_usart.o(.data)
    hadc1                                    0x20000074   Data          72  adc.o(.bss)
    hdma_adc1                                0x200000bc   Data          96  adc.o(.bss)
    htim2                                    0x2000011c   Data          72  tim.o(.bss)
    huart1                                   0x20000164   Data          72  usart.o(.bss)
    huart2                                   0x200001ac   Data          72  usart.o(.bss)
    huart3                                   0x200001f4   Data          72  usart.o(.bss)
    rxBuffer1                                0x200002dc   Data         256  my_usart.o(.bss)
    rxBuffer3                                0x200003dc   Data         256  my_usart.o(.bss)
    __libspace_start                         0x200004dc   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x2000053c   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00007a4c, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000079d8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         4309  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         4748    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000001a   Code   RO         4750    !!handler_copy      c_w.l(__scatter_copy.o)
    0x080001de   0x080001de   0x00000002   PAD
    0x080001e0   0x080001e0   0x0000001c   Code   RO         4752    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001fc   0x080001fc   0x00000000   Code   RO         4433    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO         4422    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000202   0x08000202   0x00000006   Code   RO         4424    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000208   0x08000208   0x00000006   Code   RO         4429    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800020e   0x0800020e   0x00000006   Code   RO         4430    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000214   0x08000214   0x00000006   Code   RO         4431    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800021a   0x0800021a   0x00000006   Code   RO         4432    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000220   0x08000220   0x0000000a   Code   RO         4437    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO         4426    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000230   0x08000230   0x00000006   Code   RO         4427    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000236   0x08000236   0x00000006   Code   RO         4428    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         4425    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000242   0x08000242   0x00000006   Code   RO         4423    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000248   0x08000248   0x00000006   Code   RO         4434    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         4435    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000254   0x08000254   0x00000006   Code   RO         4436    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         4441    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000260   0x08000260   0x00000006   Code   RO         4442    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x08000266   0x08000266   0x0000000a   Code   RO         4438    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000270   0x08000270   0x00000006   Code   RO         4420    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x08000276   0x08000276   0x00000006   Code   RO         4421    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         4439    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x08000282   0x08000282   0x00000006   Code   RO         4440    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000288   0x08000288   0x00000004   Code   RO         4548    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x0800028c   0x0800028c   0x00000002   Code   RO         4610    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x0800028e   0x0800028e   0x00000004   Code   RO         4628    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         4631    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         4634    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         4636    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000000   Code   RO         4638    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000292   0x08000292   0x00000006   Code   RO         4639    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x00000000   Code   RO         4641    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x08000298   0x08000298   0x0000000c   Code   RO         4642    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         4643    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x00000000   Code   RO         4645    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002a4   0x080002a4   0x0000000a   Code   RO         4646    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4647    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4649    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4651    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4653    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4655    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4657    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4659    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4661    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4665    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4667    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4669    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000000   Code   RO         4671    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002ae   0x080002ae   0x00000002   Code   RO         4672    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002b0   0x080002b0   0x00000002   Code   RO         4705    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         4731    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         4733    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         4736    .ARM.Collect$$libshutdown$$00000007  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         4739    .ARM.Collect$$libshutdown$$0000000A  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         4741    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000000   Code   RO         4744    .ARM.Collect$$libshutdown$$0000000F  c_w.l(libshutdown2.o)
    0x080002b2   0x080002b2   0x00000002   Code   RO         4745    .ARM.Collect$$libshutdown$$00000010  c_w.l(libshutdown2.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         4343    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002b4   0x080002b4   0x00000000   Code   RO         4512    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002b4   0x080002b4   0x00000006   Code   RO         4524    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x080002ba   0x080002ba   0x00000000   Code   RO         4514    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x080002ba   0x080002ba   0x00000004   Code   RO         4515    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000000   Code   RO         4517    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x080002be   0x080002be   0x00000008   Code   RO         4518    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x080002c6   0x080002c6   0x00000002   Code   RO         4617    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x080002c8   0x080002c8   0x00000000   Code   RO         4678    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         4679    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x080002cc   0x080002cc   0x00000006   Code   RO         4680    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x080002d2   0x080002d2   0x00000002   PAD
    0x080002d4   0x080002d4   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x08000314   0x08000314   0x000000ee   Code   RO         4295    .text               c_w.l(lludivv7m.o)
    0x08000402   0x08000402   0x00000002   PAD
    0x08000404   0x08000404   0x00000034   Code   RO         4297    .text               c_w.l(vsnprintf.o)
    0x08000438   0x08000438   0x0000003e   Code   RO         4299    .text               c_w.l(strlen.o)
    0x08000476   0x08000476   0x0000004e   Code   RO         4303    .text               c_w.l(rt_memclr_w.o)
    0x080004c4   0x080004c4   0x00000080   Code   RO         4305    .text               c_w.l(strcmpv7m.o)
    0x08000544   0x08000544   0x00000006   Code   RO         4307    .text               c_w.l(heapauxi.o)
    0x0800054a   0x0800054a   0x00000016   Code   RO         4348    .text               c_w.l(_rserrno.o)
    0x08000560   0x08000560   0x0000004e   Code   RO         4352    .text               c_w.l(_printf_pad.o)
    0x080005ae   0x080005ae   0x00000024   Code   RO         4354    .text               c_w.l(_printf_truncate.o)
    0x080005d2   0x080005d2   0x00000052   Code   RO         4356    .text               c_w.l(_printf_str.o)
    0x08000624   0x08000624   0x00000078   Code   RO         4358    .text               c_w.l(_printf_dec.o)
    0x0800069c   0x0800069c   0x00000028   Code   RO         4360    .text               c_w.l(_printf_charcount.o)
    0x080006c4   0x080006c4   0x00000030   Code   RO         4362    .text               c_w.l(_printf_char_common.o)
    0x080006f4   0x080006f4   0x0000000a   Code   RO         4364    .text               c_w.l(_sputc.o)
    0x080006fe   0x080006fe   0x00000010   Code   RO         4366    .text               c_w.l(_snputc.o)
    0x0800070e   0x0800070e   0x00000002   PAD
    0x08000710   0x08000710   0x000000bc   Code   RO         4368    .text               c_w.l(_printf_wctomb.o)
    0x080007cc   0x080007cc   0x0000007c   Code   RO         4371    .text               c_w.l(_printf_longlong_dec.o)
    0x08000848   0x08000848   0x00000070   Code   RO         4377    .text               c_w.l(_printf_oct_int_ll.o)
    0x080008b8   0x080008b8   0x00000094   Code   RO         4397    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800094c   0x0800094c   0x00000188   Code   RO         4417    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000ad4   0x08000ad4   0x00000008   Code   RO         4531    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000adc   0x08000adc   0x0000008a   Code   RO         4533    .text               c_w.l(lludiv10.o)
    0x08000b66   0x08000b66   0x000000b2   Code   RO         4535    .text               c_w.l(_printf_intcommon.o)
    0x08000c18   0x08000c18   0x0000041e   Code   RO         4537    .text               c_w.l(_printf_fp_dec.o)
    0x08001036   0x08001036   0x00000002   PAD
    0x08001038   0x08001038   0x000002fc   Code   RO         4539    .text               c_w.l(_printf_fp_hex.o)
    0x08001334   0x08001334   0x0000002c   Code   RO         4544    .text               c_w.l(_printf_char.o)
    0x08001360   0x08001360   0x0000002c   Code   RO         4546    .text               c_w.l(_printf_wchar.o)
    0x0800138c   0x0800138c   0x00000040   Code   RO         4549    .text               c_w.l(_wcrtomb.o)
    0x080013cc   0x080013cc   0x00000008   Code   RO         4557    .text               c_w.l(libspace.o)
    0x080013d4   0x080013d4   0x0000004a   Code   RO         4560    .text               c_w.l(sys_stackheap_outer.o)
    0x0800141e   0x0800141e   0x00000002   PAD
    0x08001420   0x08001420   0x00000010   Code   RO         4562    .text               c_w.l(rt_ctype_table.o)
    0x08001430   0x08001430   0x00000008   Code   RO         4567    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001438   0x08001438   0x00000080   Code   RO         4569    .text               c_w.l(_printf_fp_infnan.o)
    0x080014b8   0x080014b8   0x000000e4   Code   RO         4571    .text               c_w.l(bigflt0.o)
    0x0800159c   0x0800159c   0x00000012   Code   RO         4599    .text               c_w.l(exit.o)
    0x080015ae   0x080015ae   0x00000002   PAD
    0x080015b0   0x080015b0   0x0000000c   Code   RO         4675    .text               c_w.l(sys_exit.o)
    0x080015bc   0x080015bc   0x00000002   Code   RO         4694    .text               c_w.l(use_no_semi.o)
    0x080015be   0x080015be   0x00000000   Code   RO         4696    .text               c_w.l(indicate_semi.o)
    0x080015be   0x080015be   0x0000003e   Code   RO         4574    CL$$btod_d2e        c_w.l(btod.o)
    0x080015fc   0x080015fc   0x00000046   Code   RO         4576    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001642   0x08001642   0x00000060   Code   RO         4575    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x080016a2   0x080016a2   0x00000338   Code   RO         4584    CL$$btod_div_common  c_w.l(btod.o)
    0x080019da   0x080019da   0x000000dc   Code   RO         4581    CL$$btod_e2e        c_w.l(btod.o)
    0x08001ab6   0x08001ab6   0x0000002a   Code   RO         4578    CL$$btod_ediv       c_w.l(btod.o)
    0x08001ae0   0x08001ae0   0x0000002a   Code   RO         4577    CL$$btod_emul       c_w.l(btod.o)
    0x08001b0a   0x08001b0a   0x00000244   Code   RO         4583    CL$$btod_mult_common  c_w.l(btod.o)
    0x08001d4e   0x08001d4e   0x00000002   PAD
    0x08001d50   0x08001d50   0x00000010   Code   RO         3561    i.AD9959_CS_H       ad9959.o
    0x08001d60   0x08001d60   0x00000010   Code   RO         3562    i.AD9959_CS_L       ad9959.o
    0x08001d70   0x08001d70   0x00000028   Code   RO         3563    i.AD9959_Ch         ad9959.o
    0x08001d98   0x08001d98   0x00000028   Code   RO         3564    i.AD9959_IO_UpDate  ad9959.o
    0x08001dc0   0x08001dc0   0x0000003a   Code   RO         3565    i.AD9959_Init       ad9959.o
    0x08001dfa   0x08001dfa   0x00000002   PAD
    0x08001dfc   0x08001dfc   0x00000010   Code   RO         3575    i.AD9959_PDC_L      ad9959.o
    0x08001e0c   0x08001e0c   0x00000010   Code   RO         3576    i.AD9959_RST_H      ad9959.o
    0x08001e1c   0x08001e1c   0x00000010   Code   RO         3577    i.AD9959_RST_L      ad9959.o
    0x08001e2c   0x08001e2c   0x0000001e   Code   RO         3578    i.AD9959_Reset      ad9959.o
    0x08001e4a   0x08001e4a   0x00000002   PAD
    0x08001e4c   0x08001e4c   0x00000010   Code   RO         3579    i.AD9959_SCK_H      ad9959.o
    0x08001e5c   0x08001e5c   0x00000010   Code   RO         3580    i.AD9959_SCK_L      ad9959.o
    0x08001e6c   0x08001e6c   0x00000010   Code   RO         3581    i.AD9959_SDIO0_H    ad9959.o
    0x08001e7c   0x08001e7c   0x00000010   Code   RO         3582    i.AD9959_SDIO0_L    ad9959.o
    0x08001e8c   0x08001e8c   0x00000010   Code   RO         3587    i.AD9959_SDIO3_H    ad9959.o
    0x08001e9c   0x08001e9c   0x00000010   Code   RO         3588    i.AD9959_SDIO3_L    ad9959.o
    0x08001eac   0x08001eac   0x00000014   Code   RO         3589    i.AD9959_Set_Amp    ad9959.o
    0x08001ec0   0x08001ec0   0x00000030   Code   RO         3591    i.AD9959_Set_Fre    ad9959.o
    0x08001ef0   0x08001ef0   0x00000024   Code   RO         3593    i.AD9959_Set_Pha    ad9959.o
    0x08001f14   0x08001f14   0x0000002e   Code   RO         3595    i.AD9959_Single_Output  ad9959.o
    0x08001f42   0x08001f42   0x0000001a   Code   RO         3597    i.AD9959_Start      ad9959.o
    0x08001f5c   0x08001f5c   0x00000010   Code   RO         3599    i.AD9959_UP_H       ad9959.o
    0x08001f6c   0x08001f6c   0x00000010   Code   RO         3600    i.AD9959_UP_L       ad9959.o
    0x08001f7c   0x08001f7c   0x00000030   Code   RO         3601    i.AD9959_WByte      ad9959.o
    0x08001fac   0x08001fac   0x00000044   Code   RO         3602    i.AD9959_WRrg       ad9959.o
    0x08001ff0   0x08001ff0   0x0000006e   Code   RO          545    i.ADC_DMAConvCplt   stm32f4xx_hal_adc.o
    0x0800205e   0x0800205e   0x00000016   Code   RO          546    i.ADC_DMAError      stm32f4xx_hal_adc.o
    0x08002074   0x08002074   0x0000000a   Code   RO          547    i.ADC_DMAHalfConvCplt  stm32f4xx_hal_adc.o
    0x0800207e   0x0800207e   0x00000002   PAD
    0x08002080   0x08002080   0x00000128   Code   RO          548    i.ADC_Init          stm32f4xx_hal_adc.o
    0x080021a8   0x080021a8   0x00000002   Code   RO          433    i.BusFault_Handler  stm32f4xx_it.o
    0x080021aa   0x080021aa   0x00000002   PAD
    0x080021ac   0x080021ac   0x00000054   Code   RO         3607    i.Calculate_Phase_From_Delay  ad9959.o
    0x08002200   0x08002200   0x00000038   Code   RO         3347    i.Check_And_Sync_Update  bsp_system.o
    0x08002238   0x08002238   0x00000020   Code   RO         4132    i.Check_UART1_Command  my_usart.o
    0x08002258   0x08002258   0x00000070   Code   RO         4133    i.Check_UART3_Command  my_usart.o
    0x080022c8   0x080022c8   0x0000002e   Code   RO         3608    i.Convert_mV_to_AD9959_Amp  ad9959.o
    0x080022f6   0x080022f6   0x00000002   PAD
    0x080022f8   0x080022f8   0x0000000c   Code   RO          434    i.DMA2_Stream0_IRQHandler  stm32f4xx_it.o
    0x08002304   0x08002304   0x00000028   Code   RO         1282    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x0800232c   0x0800232c   0x00000054   Code   RO         1283    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002380   0x08002380   0x00000028   Code   RO         1284    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x080023a8   0x080023a8   0x00000002   Code   RO          435    i.DebugMon_Handler  stm32f4xx_it.o
    0x080023aa   0x080023aa   0x00000002   PAD
    0x080023ac   0x080023ac   0x00000010   Code   RO         3348    i.Disable_Sync_Update  bsp_system.o
    0x080023bc   0x080023bc   0x00000044   Code   RO         4134    i.Display_Relative_Delay_Info  my_usart.o
    0x08002400   0x08002400   0x00000004   Code   RO           14    i.Error_Handler     main.o
    0x08002404   0x08002404   0x0000014c   Code   RO          550    i.HAL_ADC_ConfigChannel  stm32f4xx_hal_adc.o
    0x08002550   0x08002550   0x0000004c   Code   RO         3497    i.HAL_ADC_ConvCpltCallback  measurement.o
    0x0800259c   0x0800259c   0x00000002   Code   RO          552    i.HAL_ADC_ConvHalfCpltCallback  stm32f4xx_hal_adc.o
    0x0800259e   0x0800259e   0x00000002   Code   RO          554    i.HAL_ADC_ErrorCallback  stm32f4xx_hal_adc.o
    0x080025a0   0x080025a0   0x00000054   Code   RO          559    i.HAL_ADC_Init      stm32f4xx_hal_adc.o
    0x080025f4   0x080025f4   0x00000098   Code   RO          272    i.HAL_ADC_MspInit   adc.o
    0x0800268c   0x0800268c   0x00000158   Code   RO          566    i.HAL_ADC_Start_DMA  stm32f4xx_hal_adc.o
    0x080027e4   0x080027e4   0x0000006c   Code   RO          569    i.HAL_ADC_Stop_DMA  stm32f4xx_hal_adc.o
    0x08002850   0x08002850   0x00000092   Code   RO         1285    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x080028e2   0x080028e2   0x00000024   Code   RO         1286    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002906   0x08002906   0x00000002   PAD
    0x08002908   0x08002908   0x000001a0   Code   RO         1290    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08002aa8   0x08002aa8   0x000000d4   Code   RO         1291    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08002b7c   0x08002b7c   0x0000006e   Code   RO         1295    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08002bea   0x08002bea   0x00000002   PAD
    0x08002bec   0x08002bec   0x00000024   Code   RO         1722    i.HAL_Delay         stm32f4xx_hal.o
    0x08002c10   0x08002c10   0x000001f0   Code   RO         1178    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08002e00   0x08002e00   0x0000000a   Code   RO         1182    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08002e0a   0x08002e0a   0x00000002   PAD
    0x08002e0c   0x08002e0c   0x0000000c   Code   RO         1728    i.HAL_GetTick       stm32f4xx_hal.o
    0x08002e18   0x08002e18   0x00000010   Code   RO         1734    i.HAL_IncTick       stm32f4xx_hal.o
    0x08002e28   0x08002e28   0x00000034   Code   RO         1735    i.HAL_Init          stm32f4xx_hal.o
    0x08002e5c   0x08002e5c   0x00000040   Code   RO         1736    i.HAL_InitTick      stm32f4xx_hal.o
    0x08002e9c   0x08002e9c   0x00000030   Code   RO          521    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08002ecc   0x08002ecc   0x0000001a   Code   RO         1570    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08002ee6   0x08002ee6   0x00000002   PAD
    0x08002ee8   0x08002ee8   0x00000040   Code   RO         1576    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002f28   0x08002f28   0x00000024   Code   RO         1577    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002f4c   0x08002f4c   0x00000134   Code   RO          824    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08003080   0x08003080   0x00000020   Code   RO          831    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x080030a0   0x080030a0   0x00000020   Code   RO          832    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x080030c0   0x080030c0   0x00000060   Code   RO          833    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08003120   0x08003120   0x0000036c   Code   RO          836    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x0800348c   0x0800348c   0x00000028   Code   RO         1581    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080034b4   0x080034b4   0x00000090   Code   RO         2698    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x08003544   0x08003544   0x0000005a   Code   RO         1975    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x0800359e   0x0800359e   0x00000002   PAD
    0x080035a0   0x080035a0   0x00000024   Code   RO          338    i.HAL_TIM_Base_MspInit  tim.o
    0x080035c4   0x080035c4   0x00000078   Code   RO         1978    i.HAL_TIM_Base_Start  stm32f4xx_hal_tim.o
    0x0800363c   0x0800363c   0x000000dc   Code   RO         1984    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08003718   0x08003718   0x00000002   Code   RO         2958    i.HAL_UARTEx_RxEventCallback  stm32f4xx_hal_uart.o
    0x0800371a   0x0800371a   0x00000002   Code   RO         2972    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x0800371c   0x0800371c   0x00000280   Code   RO         2975    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x0800399c   0x0800399c   0x00000064   Code   RO         2976    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08003a00   0x08003a00   0x000000f8   Code   RO          380    i.HAL_UART_MspInit  usart.o
    0x08003af8   0x08003af8   0x0000001c   Code   RO         2981    i.HAL_UART_Receive_IT  stm32f4xx_hal_uart.o
    0x08003b14   0x08003b14   0x000000bc   Code   RO         4139    i.HAL_UART_RxCpltCallback  my_usart.o
    0x08003bd0   0x08003bd0   0x000000a0   Code   RO         2984    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08003c70   0x08003c70   0x00000002   Code   RO         2987    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08003c72   0x08003c72   0x00000002   PAD
    0x08003c74   0x08003c74   0x00000038   Code   RO         4057    i.HMI_Debug_Print   my_hmi.o
    0x08003cac   0x08003cac   0x0000006c   Code   RO         4058    i.HMI_Send_Float    my_hmi.o
    0x08003d18   0x08003d18   0x00000020   Code   RO         4059    i.HMI_Send_Int      my_hmi.o
    0x08003d38   0x08003d38   0x00000020   Code   RO         4060    i.HMI_Send_String   my_hmi.o
    0x08003d58   0x08003d58   0x00000002   Code   RO          436    i.HardFault_Handler  stm32f4xx_it.o
    0x08003d5a   0x08003d5a   0x00000002   PAD
    0x08003d5c   0x08003d5c   0x00000064   Code   RO          273    i.MX_ADC1_Init      adc.o
    0x08003dc0   0x08003dc0   0x0000002c   Code   RO          313    i.MX_DMA_Init       dma.o
    0x08003dec   0x08003dec   0x00000128   Code   RO          247    i.MX_GPIO_Init      gpio.o
    0x08003f14   0x08003f14   0x00000068   Code   RO          339    i.MX_TIM2_Init      tim.o
    0x08003f7c   0x08003f7c   0x00000038   Code   RO          381    i.MX_USART1_UART_Init  usart.o
    0x08003fb4   0x08003fb4   0x00000038   Code   RO          382    i.MX_USART2_UART_Init  usart.o
    0x08003fec   0x08003fec   0x00000038   Code   RO          383    i.MX_USART3_UART_Init  usart.o
    0x08004024   0x08004024   0x0000002c   Code   RO         3499    i.Measurement_Enable  measurement.o
    0x08004050   0x08004050   0x00000104   Code   RO         3502    i.Measurement_Process_DC  measurement.o
    0x08004154   0x08004154   0x00000010   Code   RO         3503    i.Measurement_Reset_Filter  measurement.o
    0x08004164   0x08004164   0x00000002   Code   RO          437    i.MemManage_Handler  stm32f4xx_it.o
    0x08004166   0x08004166   0x00000002   Code   RO          438    i.NMI_Handler       stm32f4xx_it.o
    0x08004168   0x08004168   0x00000f00   Code   RO         4140    i.Parse_Command_UART1  my_usart.o
    0x08005068   0x08005068   0x00000002   Code   RO          439    i.PendSV_Handler    stm32f4xx_it.o
    0x0800506a   0x0800506a   0x00000002   PAD
    0x0800506c   0x0800506c   0x0000004c   Code   RO         3350    i.Process_System_State  bsp_system.o
    0x080050b8   0x080050b8   0x00000002   Code   RO          440    i.SVC_Handler       stm32f4xx_it.o
    0x080050ba   0x080050ba   0x00000002   PAD
    0x080050bc   0x080050bc   0x0000002c   Code   RO         4141    i.Send_FPGA_Command  my_usart.o
    0x080050e8   0x080050e8   0x0000006c   Code   RO         4142    i.Send_Peak_Value_To_FPGA  my_usart.o
    0x08005154   0x08005154   0x00000018   Code   RO         3353    i.Set_Parameter_Changed  bsp_system.o
    0x0800516c   0x0800516c   0x00000004   Code   RO          441    i.SysTick_Handler   stm32f4xx_it.o
    0x08005170   0x08005170   0x00000094   Code   RO           16    i.SystemClock_Config  main.o
    0x08005204   0x08005204   0x00000010   Code   RO         3310    i.SystemInit        system_stm32f4xx.o
    0x08005214   0x08005214   0x0000007c   Code   RO           17    i.System_Init_Sync_Control  main.o
    0x08005290   0x08005290   0x00000010   Code   RO           18    i.System_Init_Time_Delay  main.o
    0x080052a0   0x080052a0   0x000000d0   Code   RO         2068    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08005370   0x08005370   0x00000014   Code   RO         2079    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x08005384   0x08005384   0x00000010   Code   RO         2080    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x08005394   0x08005394   0x00000022   Code   RO         2086    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080053b6   0x080053b6   0x00000024   Code   RO         2088    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080053da   0x080053da   0x0000000e   Code   RO         2989    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x080053e8   0x080053e8   0x0000004e   Code   RO         2999    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08005436   0x08005436   0x000000c2   Code   RO         3001    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080054f8   0x080054f8   0x0000010c   Code   RO         3002    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08005604   0x08005604   0x00000036   Code   RO         3004    i.UART_Start_Receive_IT  stm32f4xx_hal_uart.o
    0x0800563a   0x0800563a   0x00000072   Code   RO         3005    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x080056ac   0x080056ac   0x0000000c   Code   RO          442    i.USART1_IRQHandler  stm32f4xx_it.o
    0x080056b8   0x080056b8   0x00000038   Code   RO         3610    i.Update_CH0_With_Time_Delay  ad9959.o
    0x080056f0   0x080056f0   0x00000038   Code   RO         3612    i.Update_CH1_With_Time_Delay  ad9959.o
    0x08005728   0x08005728   0x00000068   Code   RO         3613    i.Update_Channel_Output  ad9959.o
    0x08005790   0x08005790   0x00000002   Code   RO          443    i.UsageFault_Handler  stm32f4xx_it.o
    0x08005792   0x08005792   0x00000030   Code   RO         4494    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x080057c2   0x080057c2   0x00000020   Code   RO         1583    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080057e2   0x080057e2   0x00000006   PAD
    0x080057e8   0x080057e8   0x00000c50   Code   RO         4329    i.__hardfp_pow      m_wm.l(pow.o)
    0x08006438   0x08006438   0x000000f8   Code   RO         4496    i.__kernel_poly     m_wm.l(poly.o)
    0x08006530   0x08006530   0x00000030   Code   RO         4474    i.__mathlib_dbl_divzero  m_wm.l(dunder.o)
    0x08006560   0x08006560   0x00000014   Code   RO         4476    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x08006574   0x08006574   0x00000004   PAD
    0x08006578   0x08006578   0x00000020   Code   RO         4477    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08006598   0x08006598   0x00000020   Code   RO         4478    i.__mathlib_dbl_overflow  m_wm.l(dunder.o)
    0x080065b8   0x080065b8   0x00000020   Code   RO         4480    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x080065d8   0x080065d8   0x0000000e   Code   RO         4410    i._is_digit         c_w.l(__printf_wp.o)
    0x080065e6   0x080065e6   0x00000018   Code   RO         4490    i.fabs              m_wm.l(fabs.o)
    0x080065fe   0x080065fe   0x00000002   PAD
    0x08006600   0x08006600   0x000001e4   Code   RO           20    i.main              main.o
    0x080067e4   0x080067e4   0x00000032   Code   RO         4145    i.my_printf         my_usart.o
    0x08006816   0x08006816   0x0000006e   Code   RO         4501    i.sqrt              m_wm.l(sqrt.o)
    0x08006884   0x08006884   0x0000002c   Code   RO         4597    locale$$code        c_w.l(lc_numeric_c.o)
    0x080068b0   0x080068b0   0x0000002c   Code   RO         4622    locale$$code        c_w.l(lc_ctype_c.o)
    0x080068dc   0x080068dc   0x00000018   Code   RO         4443    x$fpl$basic         fz_wm.l(basic.o)
    0x080068f4   0x080068f4   0x00000150   Code   RO         4445    x$fpl$dadd          fz_wm.l(daddsub_clz.o)
    0x08006a44   0x08006a44   0x00000010   Code   RO         4551    x$fpl$dcheck1       fz_wm.l(dcheck1.o)
    0x08006a54   0x08006a54   0x00000018   Code   RO         4611    x$fpl$dcmpinf       fz_wm.l(dcmpi.o)
    0x08006a6c   0x08006a6c   0x000002b0   Code   RO         4452    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08006d1c   0x08006d1c   0x0000005e   Code   RO         4311    x$fpl$dfix          fz_wm.l(dfix.o)
    0x08006d7a   0x08006d7a   0x00000002   PAD
    0x08006d7c   0x08006d7c   0x0000005a   Code   RO         4315    x$fpl$dfixu         fz_wm.l(dfixu.o)
    0x08006dd6   0x08006dd6   0x0000002e   Code   RO         4320    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x08006e04   0x08006e04   0x00000026   Code   RO         4319    x$fpl$dfltu         fz_wm.l(dflt_clz.o)
    0x08006e2a   0x08006e2a   0x00000002   PAD
    0x08006e2c   0x08006e2c   0x00000078   Code   RO         4553    x$fpl$dleqf         fz_wm.l(dleqf.o)
    0x08006ea4   0x08006ea4   0x00000154   Code   RO         4325    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08006ff8   0x08006ff8   0x0000009c   Code   RO         4455    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08007094   0x08007094   0x0000000c   Code   RO         4457    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x080070a0   0x080070a0   0x0000006c   Code   RO         4459    x$fpl$drleqf        fz_wm.l(drleqf.o)
    0x0800710c   0x0800710c   0x00000016   Code   RO         4446    x$fpl$drsb          fz_wm.l(daddsub_clz.o)
    0x08007122   0x08007122   0x00000002   PAD
    0x08007124   0x08007124   0x00000198   Code   RO         4555    x$fpl$dsqrt         fz_wm.l(dsqrt_umaal.o)
    0x080072bc   0x080072bc   0x000001d4   Code   RO         4447    x$fpl$dsub          fz_wm.l(daddsub_clz.o)
    0x08007490   0x08007490   0x00000056   Code   RO         4327    x$fpl$f2d           fz_wm.l(f2d.o)
    0x080074e6   0x080074e6   0x0000008c   Code   RO         4461    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x08007572   0x08007572   0x0000000a   Code   RO         4690    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800757c   0x0800757c   0x00000004   Code   RO         4463    x$fpl$printf1       fz_wm.l(printf1.o)
    0x08007580   0x08007580   0x00000004   Code   RO         4465    x$fpl$printf2       fz_wm.l(printf2.o)
    0x08007584   0x08007584   0x00000064   Code   RO         4615    x$fpl$retnan        fz_wm.l(retnan.o)
    0x080075e8   0x080075e8   0x0000005c   Code   RO         4471    x$fpl$scalbn        fz_wm.l(scalbn.o)
    0x08007644   0x08007644   0x00000030   Code   RO         4673    x$fpl$trapveneer    fz_wm.l(trapv.o)
    0x08007674   0x08007674   0x00000000   Code   RO         4473    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x08007674   0x08007674   0x00000008   Data   RO         1297    .constdata          stm32f4xx_hal_dma.o
    0x0800767c   0x0800767c   0x00000010   Data   RO         3311    .constdata          system_stm32f4xx.o
    0x0800768c   0x0800768c   0x00000008   Data   RO         3312    .constdata          system_stm32f4xx.o
    0x08007694   0x08007694   0x00000004   PAD
    0x08007698   0x08007698   0x00000088   Data   RO         4332    .constdata          m_wm.l(pow.o)
    0x08007720   0x08007720   0x00000008   Data   RO         4369    .constdata          c_w.l(_printf_wctomb.o)
    0x08007728   0x08007728   0x00000028   Data   RO         4398    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x08007750   0x08007750   0x00000011   Data   RO         4418    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08007761   0x08007761   0x00000007   PAD
    0x08007768   0x08007768   0x00000008   Data   RO         4498    .constdata          m_wm.l(qnan.o)
    0x08007770   0x08007770   0x00000026   Data   RO         4540    .constdata          c_w.l(_printf_fp_hex.o)
    0x08007796   0x08007796   0x00000002   PAD
    0x08007798   0x08007798   0x00000094   Data   RO         4572    .constdata          c_w.l(bigflt0.o)
    0x0800782c   0x0800782c   0x0000005e   Data   RO         4148    .conststring        my_usart.o
    0x0800788a   0x0800788a   0x00000002   PAD
    0x0800788c   0x0800788c   0x00000020   Data   RO         4746    Region$$Table       anon$$obj.o
    0x080078ac   0x080078ac   0x0000001c   Data   RO         4596    locale$$data        c_w.l(lc_numeric_c.o)
    0x080078c8   0x080078c8   0x00000110   Data   RO         4621    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080079d8, Size: 0x00000b40, Max: 0x0001c000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080079d8   0x0000000c   Data   RW         1742    .data               stm32f4xx_hal.o
    0x2000000c   0x080079e4   0x00000004   Data   RW         3313    .data               system_stm32f4xx.o
    0x20000010   0x080079e8   0x00000020   Data   RW         3359    .data               bsp_system.o
    0x20000030   0x08007a08   0x00000004   Data   RW         3360    .data               bsp_system.o
    0x20000034   0x08007a0c   0x00000001   Data   RW         3361    .data               bsp_system.o
    0x20000035   0x08007a0d   0x00000001   PAD
    0x20000036   0x08007a0e   0x00000002   Data   RW         3362    .data               bsp_system.o
    0x20000038   0x08007a10   0x00000002   Data   RW         3363    .data               bsp_system.o
    0x2000003a   0x08007a12   0x00000002   PAD
    0x2000003c   0x08007a14   0x0000000c   Data   RW         3505    .data               measurement.o
    0x20000048   0x08007a20   0x00000021   Data   RW         3615    .data               ad9959.o
    0x20000069   0x08007a41   0x00000001   PAD
    0x2000006a   0x08007a42   0x0000000a   Data   RW         4149    .data               my_usart.o
    0x20000074        -       0x000000a8   Zero   RW          274    .bss                adc.o
    0x2000011c        -       0x00000048   Zero   RW          340    .bss                tim.o
    0x20000164        -       0x000000d8   Zero   RW          384    .bss                usart.o
    0x2000023c        -       0x000000a0   Zero   RW         3504    .bss                measurement.o
    0x200002dc        -       0x00000200   Zero   RW         4146    .bss                my_usart.o
    0x200004dc        -       0x00000060   Zero   RW         4558    .bss                c_w.l(libspace.o)
    0x2000053c   0x08007a4c   0x00000004   PAD
    0x20000540        -       0x00000200   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x20000740        -       0x00000400   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x08007a4c, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1014        160          0         33          0      17582   ad9959.o
       252         30          0          0        168       1775   adc.o
       172         22          0         41          0       5324   bsp_system.o
        44          4          0          0          0        774   dma.o
       296         20          0          0          0       1055   gpio.o
       776        332          0          0          0     711742   main.o
       396        100          0         12        160       3572   measurement.o
       228         92          0          0          0       3600   my_hmi.o
      4442       2270         94         10        512       8155   my_usart.o
        64         26        392          0       1536        844   startup_stm32f407xx.o
       180         28          0         12          0       9401   stm32f4xx_hal.o
      1310         66          0          0          0       7745   stm32f4xx_hal_adc.o
       198         14          0          0          0      33831   stm32f4xx_hal_cortex.o
      1084         16          8          0          0       7338   stm32f4xx_hal_dma.o
       506         46          0          0          0       2192   stm32f4xx_hal_gpio.o
        48          6          0          0          0        858   stm32f4xx_hal_msp.o
      1344         72          0          0          0       5252   stm32f4xx_hal_rcc.o
       744         74          0          0          0       7024   stm32f4xx_hal_tim.o
       144         28          0          0          0       1372   stm32f4xx_hal_tim_ex.o
      1656         14          0          0          0      11424   stm32f4xx_hal_uart.o
        44         12          0          0          0       5100   stm32f4xx_it.o
        16          4         24          4          0       1127   system_stm32f4xx.o
       140         12          0          0         72       1662   tim.o
       416         48          0          0        216       3128   usart.o

    ----------------------------------------------------------------------
     15550       <USER>        <GROUP>        116       2664     851877   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        36          0          6          4          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         6          0          0          0          0        152   heapauxi.o
         0          0          0          0          0          0   indicate_semi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        34          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        62          0          0          0          0         76   strlen.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        24          0          0          0          0        164   basic.o
       826         16          0          0          0        492   daddsub_clz.o
        16          4          0          0          0        116   dcheck1.o
        24          0          0          0          0        116   dcmpi.o
       688        140          0          0          0        256   ddiv.o
        94          4          0          0          0        140   dfix.o
        90          4          0          0          0        140   dfixu.o
        84          0          0          0          0        232   dflt_clz.o
       120          4          0          0          0        140   dleqf.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
       108          0          0          0          0        128   drleqf.o
       408         56          0          0          0        168   dsqrt_umaal.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
       100          0          0          0          0        116   retnan.o
        92          0          0          0          0        116   scalbn.o
        48          0          0          0          0        116   trapv.o
         0          0          0          0          0          0   usenofp.o
       164         44          0          0          0        620   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
      3152        296        136          0          0        352   pow.o
         0          0          8          0          0          0   qnan.o
       110          0          0          0          0        148   sqrt.o

    ----------------------------------------------------------------------
     14382        <USER>        <GROUP>          0        100       9368   Library Totals
        28          0          9          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      7134        270        551          0         96       4388   c_w.l
      3474        252          0          0          0       3460   fz_wm.l
      3746        340        144          0          0       1520   m_wm.l

    ----------------------------------------------------------------------
     14382        <USER>        <GROUP>          0        100       9368   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     29932       4358       1260        116       2764     842853   Grand Totals
     29932       4358       1260        116       2764     842853   ELF Image Totals
     29932       4358       1260        116          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                31192 (  30.46kB)
    Total RW  Size (RW Data + ZI Data)              2880 (   2.81kB)
    Total ROM Size (Code + RO Data + RW Data)      31308 (  30.57kB)

==============================================================================

