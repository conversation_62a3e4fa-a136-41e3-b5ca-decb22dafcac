#ifndef MY_USART_H
#define MY_USART_H

#include "stm32f4xx_hal.h"
#include "usart.h"
#include "string.h"
#include "stdarg.h"
#include "stdio.h"
#include "commond_init.h"

// 缓冲区大小定义
#define RX_BUFFER_SIZE 256

// 声明外部串口句柄
extern UART_HandleTypeDef huart1;
extern UART_HandleTypeDef huart2;
extern UART_HandleTypeDef huart3;

// 声明外部缓冲区变量
extern uint8_t rxBuffer1[RX_BUFFER_SIZE];
extern uint8_t rxTemp1;
extern uint16_t rxIndex1;
extern volatile uint8_t commandReceived1;

extern uint8_t rxBuffer3[RX_BUFFER_SIZE];
extern uint8_t rxTemp3;
extern uint16_t rxIndex3;
extern volatile uint8_t commandReceived3;

extern uint8_t rxBuffer2[RX_BUFFER_SIZE];
extern uint8_t rxTemp2;
extern uint16_t rxIndex2;
extern volatile uint8_t frameStarted;

extern u8 Adjust;

// ========== FPGA参数声明 ==========
extern uint8_t modulation_depth_ch0; // CH0调制度
extern uint8_t modulation_depth_ch1; // CH1调制度
extern uint16_t time_delay_ns_ch0;   // CH0时延
extern uint16_t time_delay_ns_ch1;   // CH1时延

// 函数声明
int my_printf(UART_HandleTypeDef *huart, const char *format, ...);
void Parse_Command_UART1(char *command);
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart);
void USART1_Init_IT(void);
void USART3_Init_IT(void);
void Check_UART1_Command(void);
void Check_UART3_Command(void);

// =================================================================
//                      时延控制函数声明 (添加到ad9959.h)
// =================================================================

/**
 * @brief 根据当前时延设置更新CH0相位输出
 */
void Update_CH0_With_Time_Delay(void);

/**
 * @brief CH0频率变化时的时延联动更新
 */
void Update_CH0_Frequency_With_Delay_Linkage(uint32_t new_freq);

/**
 * @brief CH1频率变化时的时延联动更新
 */
void Update_CH1_Frequency_With_Delay_Linkage(uint32_t new_freq);

// ========== FPGA控制函数声明 ==========
void Send_FPGA_Command(const char *cmd_str);
uint8_t Get_CH0_Modulation_Depth(void);
uint8_t Get_CH1_Modulation_Depth(void);
uint16_t Get_CH0_Time_Delay(void);
uint16_t Get_CH1_Time_Delay(void);

#endif // MY_USART_H
