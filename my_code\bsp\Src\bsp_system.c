#include "bsp_system.h"
#include "measurement.h" // 需要调用测量相关的函数
#include "main.h"        // 需要HAL_GetTick
#include "AD9959.h"

// =================================================================
//                      全局变量定义
// =================================================================

// --- 系统状态变量定义 ---
SystemState_t system_state = SYSTEM_IDLE;
uint32_t last_parameter_change_time = 0;
uint32_t last_measurement_time = 0;

// --- 标志位定义 ---
volatile uint8_t parameter_changed_flag = 0;
volatile uint8_t measurement_enable = 1;
volatile uint8_t adc_processing_flag = 0; // 互斥锁
volatile uint8_t fft_calculate_flag = 0;  // 触发标志

// --- CH0 参数定义 ---
#define AMP_INIT_MV 125
#define PHASE_INIT_DEG 0
uint32_t freq_value_ch0 = 35000000;             // 35MHz
uint16_t amp_value_mv_ch0 = AMP_INIT_MV;        // 125mV
uint16_t phase_value_deg_ch0 = PHASE_INIT_DEG;  // 0度
uint16_t time_delay_ns_ch0 = TIME_DELAY_MIN_NS; // CH0时延，默认50ns

// --- CH1 参数定义 ---
uint32_t freq_value_ch1 = 35000000;                  // 35MHz
uint16_t amp_value_mv_ch1 = AMP_INIT_MV;             // 125mV
uint16_t phase_value_deg_ch1 = PHASE_INIT_DEG + 30;  // 30度
uint16_t time_delay_ns_ch1 = TIME_DELAY_MIN_NS + 50; // CH1时延，默认100ns（相对CH0延迟50ns）

// =================================================================
//                      双通道同步控制标志位定义
// =================================================================
volatile uint8_t ch0_param_ready_flag = 0; // CH0参数就绪标志
volatile uint8_t ch1_param_ready_flag = 0; // CH1参数就绪标志
volatile uint8_t sync_update_enable = 0;   // 同步更新使能标志，默认关闭

// =================================================================
//                      私有宏定义
// =================================================================
#define SIGNAL_SETTLE_TIME 100 // 信号稳定时间100ms

// =================================================================
//                      函数声明
// =================================================================
extern void Measurement_Disable(void);
extern void Measurement_Enable(void);
extern void Measurement_Process_DC(void);
extern void Measurement_Reset_Filter(void);
extern void Update_Channel_Output(uint8_t channel);

// =================================================================
//                      公有函数实现
// =================================================================

/**
 * @brief 设置参数变更标志
 */
void Set_Parameter_Changed(void)
{
    parameter_changed_flag = 1;
    last_parameter_change_time = HAL_GetTick();
    system_state = SYSTEM_PARAMETER_CHANGED;
}

/**
 * @brief 处理系统状态机
 */
void Process_System_State(void)
{
    uint32_t current_time = HAL_GetTick();

    switch (system_state)
    {
    case SYSTEM_IDLE:
        // 如果触发标志被设置，并且ADC没有在处理中
        if (fft_calculate_flag && !adc_processing_flag)
        {
            fft_calculate_flag = 0;
            // system_state = SYSTEM_MEASURING; // 如果测量很快，可以不切换状态
            Measurement_Process_DC(); // 调用新的测量处理函数
            // system_state = SYSTEM_IDLE;
        }
        break;

    case SYSTEM_PARAMETER_CHANGED:
        // 等待信号稳定
        if (current_time - last_parameter_change_time >= SIGNAL_SETTLE_TIME)
        {
            system_state = SYSTEM_SETTLING;
            Measurement_Reset_Filter(); // 重置测量模块的滤波器
        }
        break;

    case SYSTEM_SETTLING:
        // 信号稳定完成，返回空闲状态
        system_state = SYSTEM_IDLE;
        parameter_changed_flag = 0;
        break;

    case SYSTEM_MEASURING:
        // 测量状态处理（当前设计是阻塞式调用，暂时用不到）
        break;
    }
}

// =================================================================
//                      参数更新函数
// =================================================================

/**
 * @brief 更新CH0频率
 * @param new_freq 新频率值
 */
void Update_Frequency_CH0(uint32_t new_freq)
{
    Measurement_Disable();
    freq_value_ch0 = new_freq;
    Update_Channel_Output(0);
    Set_Parameter_Changed();
    Measurement_Enable();
}

/**
 * @brief 更新CH0幅度
 * @param new_amp 新幅度值
 */
void Update_Amplitude_CH0(uint16_t new_amp)
{
    Measurement_Disable();
    amp_value_mv_ch0 = new_amp;
    Update_Channel_Output(0);
    Set_Parameter_Changed();
    Measurement_Enable();
}

/**
 * @brief 更新CH1频率
 * @param new_freq 新频率值
 */
void Update_Frequency_CH1(uint32_t new_freq)
{
    Measurement_Disable();
    freq_value_ch1 = new_freq;
    Update_Channel_Output(1);
    Set_Parameter_Changed();
    Measurement_Enable();
}

/**
 * @brief 更新CH1幅度
 * @param new_amp 新幅度值
 */
void Update_Amplitude_CH1(uint16_t new_amp)
{
    Measurement_Disable();
    amp_value_mv_ch1 = new_amp;
    Update_Channel_Output(1);
    Set_Parameter_Changed();
    Measurement_Enable();
}

// =================================================================
//                      双通道同步控制函数实现
// =================================================================

/**
 * @brief 启用双通道同步更新模式
 */
void Enable_Sync_Update(void)
{
    sync_update_enable = 1;
    // 清除标志位，开始新的同步周期
    ch0_param_ready_flag = 0;
    ch1_param_ready_flag = 0;
}

/**
 * @brief 禁用双通道同步更新模式
 */
void Disable_Sync_Update(void)
{
    sync_update_enable = 0;
    // 清除标志位
    ch0_param_ready_flag = 0;
    ch1_param_ready_flag = 0;
}

/**
 * @brief 设置CH0参数就绪标志
 */
void Set_CH0_Ready(void)
{
    if (sync_update_enable)
    {
        ch0_param_ready_flag = !ch0_param_ready_flag; // 翻转标志位
    }
}

/**
 * @brief 设置CH1参数就绪标志
 */
void Set_CH1_Ready(void)
{
    if (sync_update_enable)
    {
        ch1_param_ready_flag = !ch1_param_ready_flag; // 翻转标志位
    }
}

/**
 * @brief 检查并执行同步更新
 * @return 1-执行了更新, 0-未执行更新
 */
uint8_t Check_And_Sync_Update(void)
{
    static uint8_t last_ch0_flag = 0;
    static uint8_t last_ch1_flag = 0;

    if (!sync_update_enable)
    {
        return 0; // 同步模式未启用
    }

    // 检测到两个标志位都有变化
    if ((ch0_param_ready_flag != last_ch0_flag) && (ch1_param_ready_flag != last_ch1_flag))
    {
        AD9959_IO_UpDate(); // 执行同步更新

        // 更新历史标志位
        last_ch0_flag = ch0_param_ready_flag;
        last_ch1_flag = ch1_param_ready_flag;

        // 设置系统参数变更标志
        Set_Parameter_Changed();

        return 1; // 执行了更新
    }

    return 0; // 未执行更新
}

/**
 * @brief 双通道同步参数更新（不立即执行IO_Update）
 * @param ch0_freq CH0频率
 * @param ch0_phase CH0相位
 * @param ch0_amp CH0幅度
 * @param ch1_freq CH1频率
 * @param ch1_phase CH1相位
 * @param ch1_amp CH1幅度
 */
void Update_Dual_Channel_Sync(uint32_t ch0_freq, float ch0_phase, uint16_t ch0_amp,
                              uint32_t ch1_freq, float ch1_phase, uint16_t ch1_amp)
{
    if (!sync_update_enable)
    {
        // 非同步模式，直接更新
        Update_Channel_Output(0);
        Update_Channel_Output(1);
        return;
    }

    // 暂时禁用测量
    Measurement_Disable();

    // 更新全局参数
    freq_value_ch0 = ch0_freq;
    phase_value_deg_ch0 = (uint16_t)ch0_phase;
    amp_value_mv_ch0 = ch0_amp;

    freq_value_ch1 = ch1_freq;
    phase_value_deg_ch1 = (uint16_t)ch1_phase;
    amp_value_mv_ch1 = ch1_amp;

    // 写入CH0参数到寄存器（不执行update）
    AD9959_Ch(0);
    AD9959_Set_Fre_NoUpdate(ch0_freq);
    AD9959_Set_Pha_NoUpdate(ch0_phase);
    AD9959_Set_Amp_NoUpdate(Convert_mV_to_AD9959_Amp(ch0_amp));
    Set_CH0_Ready(); // 设置CH0就绪标志

    // 写入CH1参数到寄存器（不执行update）
    AD9959_Ch(1);
    AD9959_Set_Fre_NoUpdate(ch1_freq);
    AD9959_Set_Pha_NoUpdate(ch1_phase);
    AD9959_Set_Amp_NoUpdate(Convert_mV_to_AD9959_Amp(ch1_amp));
    Set_CH1_Ready(); // 设置CH1就绪标志

    // 检查并执行同步更新
    Check_And_Sync_Update();

    // 重新启用测量
    Measurement_Enable();
}
