#ifndef __TEST_FUNCTIONS_H__
#define __TEST_FUNCTIONS_H__

#include "main.h"

// =================================================================
//                      基础控制函数
// =================================================================

/**
 * @brief 直接设置频率和幅度 - 最简单的控制函数
 * @param channel 通道号 (0或1)
 * @param freq_mhz 频率 (MHz)
 * @param amp_mv 幅度 (mV)
 */
void Set_Freq_Amp(uint8_t channel, float freq_mhz, uint16_t amp_mv);

/**
 * @brief 快速设置CH0
 * @param freq_mhz 频率 (MHz)
 * @param amp_mv 幅度 (mV)
 */
void Set_CH0(float freq_mhz, uint16_t amp_mv);

/**
 * @brief 快速设置CH1
 * @param freq_mhz 频率 (MHz)
 * @param amp_mv 幅度 (mV)
 */
void Set_CH1(float freq_mhz, uint16_t amp_mv);

// =================================================================
//                      测试函数
// =================================================================

/**
 * @brief 测试函数1：频率扫描测试 (30-40MHz)
 */
void Test1_Frequency_Sweep(void);

/**
 * @brief 测试函数2：幅度扫描测试 (25-250mV)
 */
void Test2_Amplitude_Sweep(void);

/**
 * @brief 测试函数3：双通道对比测试
 */
void Test3_Dual_Channel(void);

/**
 * @brief 测试函数4：快速切换测试
 */
void Test4_Fast_Switch(void);

/**
 * @brief 运行所有测试
 */
void Run_All_Tests(void);

// =================================================================
//                      预设函数
// =================================================================

/**
 * @brief 预设1：标准测试信号 (35MHz, 125mV)
 */
void Preset1_Standard(void);

/**
 * @brief 预设2：高频低幅 (40MHz, 50mV)
 */
void Preset2_HighFreq_LowAmp(void);

/**
 * @brief 预设3：低频高幅 (30MHz, 200mV)
 */
void Preset3_LowFreq_HighAmp(void);

/**
 * @brief 预设4：频率差测试 (35MHz vs 30MHz)
 */
void Preset4_FreqDiff(void);

/**
 * @brief 预设5：幅度差测试 (200mV vs 50mV)
 */
void Preset5_AmpDiff(void);

#endif // __TEST_FUNCTIONS_H__
