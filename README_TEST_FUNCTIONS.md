# AD9959 简化测试函数使用说明

## 概述
本项目已经简化了AD9959的控制代码，提供了直接控制频率和幅度的简单函数。

## 主要简化内容

### 1. 删除的冗杂代码
- 复杂的HMI显示代码
- OLED显示相关代码
- 复杂的测量和FFT功能
- 时延控制和同步控制
- 复杂的系统状态机

### 2. 保留的核心功能
- AD9959基础驱动函数
- 串口通信功能
- 简单的频率和幅度控制

## 核心控制函数

### 基础控制函数
```c
// 设置指定通道的频率和幅度
void Set_Freq_Amp(uint8_t channel, float freq_mhz, uint16_t amp_mv);

// 快速设置CH0
void Set_CH0(float freq_mhz, uint16_t amp_mv);

// 快速设置CH1  
void Set_CH1(float freq_mhz, uint16_t amp_mv);
```

### 使用示例
```c
// 设置CH0为35MHz，125mV
Set_CH0(35.0f, 125);

// 设置CH1为32MHz，100mV
Set_CH1(32.0f, 100);

// 或者使用通用函数
Set_Freq_Amp(0, 35.0f, 125); // CH0
Set_Freq_Amp(1, 32.0f, 100); // CH1
```

## 测试函数

### 1. 频率扫描测试
```c
Test1_Frequency_Sweep();
```
- 功能：CH0从30MHz扫描到40MHz，步进1MHz
- 幅度：固定125mV
- 每个频率停留500ms

### 2. 幅度扫描测试
```c
Test2_Amplitude_Sweep();
```
- 功能：CH0幅度从25mV扫描到250mV，10个步进
- 频率：固定35MHz
- 每个幅度停留500ms

### 3. 双通道对比测试
```c
Test3_Dual_Channel();
```
- 功能：测试4组不同的双通道参数组合
- 每组参数停留1秒

### 4. 快速切换测试
```c
Test4_Fast_Switch();
```
- 功能：在两组参数间快速切换20次
- 切换间隔：200ms

### 5. 运行所有测试
```c
Run_All_Tests();
```
- 功能：依次运行上述所有测试
- 测试完成后恢复默认设置

## 预设函数

### 1. 标准测试信号
```c
Preset1_Standard();
```
- CH0: 35MHz, 125mV
- CH1: 35MHz, 125mV

### 2. 高频低幅
```c
Preset2_HighFreq_LowAmp();
```
- CH0: 40MHz, 50mV
- CH1: 38MHz, 75mV

### 3. 低频高幅
```c
Preset3_LowFreq_HighAmp();
```
- CH0: 30MHz, 200mV
- CH1: 32MHz, 250mV

### 4. 频率差测试
```c
Preset4_FreqDiff();
```
- CH0: 35MHz, 125mV
- CH1: 30MHz, 125mV (5MHz频率差)

### 5. 幅度差测试
```c
Preset5_AmpDiff();
```
- CH0: 35MHz, 200mV
- CH1: 35MHz, 50mV (150mV幅度差)

## 参数范围

### 频率范围
- 最小：30MHz
- 最大：40MHz
- 建议步进：1MHz

### 幅度范围
- 最小：25mV
- 最大：250mV
- 建议步进：25mV

## 在main函数中的使用

### 方法1：在初始化后直接调用
```c
// 在main函数的初始化部分
Set_CH0(35.0f, 125);
Set_CH1(32.0f, 100);
```

### 方法2：在主循环中调用测试
```c
// 在main函数的while循环中
while (1)
{
    Test1_Frequency_Sweep(); // 运行频率扫描测试
    HAL_Delay(5000);         // 等待5秒
    
    Test2_Amplitude_Sweep(); // 运行幅度扫描测试
    HAL_Delay(5000);         // 等待5秒
}
```

### 方法3：根据需要调用预设
```c
// 在需要的地方调用预设
Preset1_Standard();      // 设置标准信号
HAL_Delay(2000);

Preset4_FreqDiff();      // 设置频率差测试
HAL_Delay(2000);
```

## 串口输出
所有函数都会通过串口1输出状态信息，波特率115200，可以通过串口助手查看：
- 参数设置确认
- 测试进度信息
- 测试完成提示

## 注意事项
1. 所有频率参数使用MHz单位的浮点数
2. 所有幅度参数使用mV单位的整数
3. 通道号：0表示CH0，1表示CH1
4. 函数调用后会立即生效，无需额外的更新操作
5. 建议在参数切换间加入适当延时，确保信号稳定
